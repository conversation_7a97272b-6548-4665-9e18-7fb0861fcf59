<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法弈 - 智能法律诉讼辅助系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 50%, #f0f8f5 100%);
            min-height: 100vh;
            color: #333333;
            overflow-x: hidden;
        }

        /* 添加背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(179, 205, 224, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(162, 217, 206, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(245, 166, 35, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 页面切换 */
        .page {
            display: none;
            min-height: 100vh;
            animation: fadeIn 0.8s ease;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 欢迎页样式 */
        .welcome-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem;
            text-align: center;
        }

        .logo-text {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .tagline {
            font-size: 1.2rem;
            color: #666;
            font-weight: 300;
            letter-spacing: 2px;
            margin-bottom: 4rem;
        }

        /* 功能网格 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            max-width: 800px;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(179, 205, 224, 0.3);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
        }

        /* 为不同功能卡片设置不同的图标颜色 */
        .feature-card:nth-child(1) .feature-icon {
            color: #B3CDE0; /* 案件智能解构 - 蓝色 */
        }

        .feature-card:nth-child(2) .feature-icon {
            color: #A2D9CE; /* 证据链诊断 - 绿色 */
        }

        .feature-card:nth-child(3) .feature-icon {
            color: #F5A623; /* 法律与案例指引 - 橙色 */
        }

        .feature-card:nth-child(4) .feature-icon {
            color: #9b59b6; /* 诉讼文书生成 - 紫色 */
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.6;
        }

        /* 按钮样式 */
        .start-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 3rem;
            font-size: 1.2rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 体验页样式 */
        .experience-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 3rem;
            font-weight: 300;
        }

        .input-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            padding: 4rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            min-height: 600px;
        }

        .input-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2.5rem;
            margin-bottom: 3rem;
        }

        .input-group {
            position: relative;
        }

        .input-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .input-group label::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .input-group textarea {
            width: 100%;
            min-height: 250px;
            padding: 2rem;
            border: 2px solid #e8ecf0;
            border-radius: 16px;
            font-family: inherit;
            font-size: 1.1rem;
            line-height: 1.7;
            resize: vertical;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: #fafbfc;
            color: #2c3e50;
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: #ffffff;
            transform: translateY(-2px);
        }

        .input-group textarea::placeholder {
            color: #a0a6b1;
            font-style: italic;
        }

        .example-btn {
            display: none;
        }

        .analyze-section {
            text-align: center;
            position: relative;
        }

        .analyze-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.2rem 3rem;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .analyze-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .analyze-btn:hover::before {
            left: 100%;
        }

        .analyze-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .analyze-btn:active {
            transform: translateY(-1px);
        }

        /* 分析过程展示区 */
        .analysis-section {
            margin: 4rem 0;
            border-radius: 20px;
            overflow: hidden;
        }

        .analysis-background {
            position: relative;
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(162, 217, 206, 0.1) 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #networkCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.6;
        }

        .analysis-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .analysis-text h3 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .analysis-stage {
            font-size: 1rem;
            color: #666;
            margin-bottom: 1.5rem;
            font-weight: 400;
            transition: color 0.3s ease;
        }

        .analysis-stage.completed {
            color: #28a745;
            font-weight: 500;
        }

        /* 进度条样式 */
        .progress-container {
            width: 100%;
            max-width: 400px;
            margin: 1.5rem auto;
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(179, 205, 224, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #B3CDE0 0%, #A2D9CE 50%, #B3CDE0 100%);
            border-radius: 12px;
            width: 0%;
            transition: width 0.5s ease-out;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(179, 205, 224, 0.3);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .progress-dots {
            display: inline-flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .progress-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            animation: pulse 1s infinite;
        }

        .progress-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .progress-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        /* 消息加载动画 */
        .loading-dots {
            display: inline-flex;
            gap: 0.3rem;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 0;
        }

        .loading-dots span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #667eea;
            animation: messagePulse 0.8s infinite;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: 0.15s;
        }

        .loading-dots span:nth-child(3) {
            animation-delay: 0.3s;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.4; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.15); }
        }

        @keyframes messagePulse {
            0%, 100% { opacity: 0.4; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .view-report-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 2rem;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }

        .view-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 结果展示区 */
        .results-section {
            margin-top: 4rem;
        }

        .evidence-chain {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-chain h3 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 500;
        }

        .evidence-timeline {
            position: relative;
            padding: 2rem 0;
            min-height: 1050px;
        }

        .evidence-container {
            display: grid;
            grid-template-columns: 250px 1fr 250px;
            gap: 2rem;
            height: 800px;
            width: 100%;
            max-width: 1600px;
            margin: 0 auto;
        }

        .evidence-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-list h4 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .evidence-list-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #B3CDE0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .evidence-list-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(179, 205, 224, 0.3);
        }

        .evidence-list-item.evidence_owned {
            border-left-color: #A2D9CE;
        }

        .evidence-list-item.evidence_owned::before {
            content: '■';
            color: #A2D9CE;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.evidence_gap {
            border-left-color: #ff6b6b;
        }

        .evidence-list-item.evidence_gap::before {
            content: '●';
            color: #ff6b6b;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.conclusion {
            border-left-color: #F5A623;
        }

        .evidence-list-item.conclusion::before {
            content: '♦';
            color: #F5A623;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.final_claim {
            border-left-color: #9b59b6;
        }

        .evidence-list-item.final_claim::before {
            content: '▲';
            color: #9b59b6;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item h5 {
            color: #333;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .evidence-list-item p {
            color: #666;
            font-size: 0.8rem;
            line-height: 1.4;
            margin: 0;
        }

        .evidence-graph {
            position: relative;
            width: 100%;
            height: 1050px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 16px;
            overflow: visible;
            padding: 20px;
            box-sizing: border-box;
        }

        .evidence-report {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-report h4 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .report-section {
            margin-bottom: 1.5rem;
        }

        .report-section h5 {
            color: #B3CDE0;
            font-size: 1rem;
            margin-bottom: 0.8rem;
            font-weight: 500;
        }

        .report-section p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 0.8rem;
        }

        .report-highlight {
            background: rgba(245, 166, 35, 0.1);
            border-left: 3px solid #F5A623;
            padding: 0.8rem;
            border-radius: 6px;
            margin: 0.5rem 0;
        }

        .evidence-node {
            position: absolute;
            background: white;
            border-radius: 14px;
            padding: 0.8rem;
            width: 120px;
            height: 75px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 3px solid #B3CDE0;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
            animation: nodeAppear 0.8s ease forwards;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .evidence-node:hover {
            z-index: 10;
        }

        .evidence-node.evidence_owned {
            border-color: #A2D9CE;
            background: rgba(162, 217, 206, 0.1);
            border-radius: 8px; /* 圆角矩形 - 已有证据 */
            border-width: 4px;
            border-style: solid;
        }

        .evidence-node.evidence_owned:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(162, 217, 206, 0.4);
        }

        .evidence-node.evidence_gap {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 50%; /* 圆形 - 证据缺口 */
            border-style: dashed;
            border-width: 4px;
        }

        .evidence-node.evidence_gap:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .evidence-node.conclusion {
            border-color: #F5A623;
            background: rgba(245, 166, 35, 0.1);
            border-radius: 50%; /* 圆形 - 结论节点 */
            border-width: 4px;
            border-style: solid;
            width: 120px; /* 缩小尺寸并确保正圆形 */
            height: 120px;
            min-width: 120px; /* 防止内容挤压变形 */
            min-height: 120px;
        }

        .evidence-node.conclusion:hover {
            transform: scale(1.05); /* 移除旋转，只保持放大 */
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.4);
        }

        .evidence-node.final_claim {
            border-color: #9b59b6;
            background: rgba(155, 89, 182, 0.1);
            border-radius: 50%; /* 圆形 - 最终诉求 */
            border-width: 4px;
            border-style: solid;
            width: 120px; /* 缩小尺寸并确保正圆形 */
            height: 120px;
            min-width: 120px; /* 防止内容挤压变形 */
            min-height: 120px;
            position: relative;
        }

        .evidence-node.final_claim:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
        }

        .evidence-node.evidence_gap::before {
            content: '⚠️';
            position: absolute;
            top: 0.3rem;
            right: 0.3rem;
            font-size: 0.9rem;
        }

        @keyframes nodeAppear {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .evidence-node h4 {
            color: #333;
            margin: 0;
            font-weight: 500;
            font-size: 0.85rem;
            line-height: 1.2;
            text-align: center;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        /* 确保所有节点类型的文字样式统一 */
        .evidence-node.evidence_owned h4,
        .evidence-node.evidence_gap h4,
        .evidence-node.conclusion h4,
        .evidence-node.final_claim h4 {
            color: #333;
            margin: 0;
            font-weight: 500;
            font-size: 0.85rem;
            line-height: 1.2;
            text-align: center;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .evidence-connection {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .evidence-connection svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .evidence-connection line {
            stroke: #B3CDE0;
            stroke-width: 3;
            opacity: 0;
            animation: lineAppear 1s ease forwards;
            marker-end: url(#arrowhead);
        }

        .evidence-connection.gap line {
            stroke: #ff6b6b;
            stroke-width: 3;
            stroke-dasharray: 8,4;
            marker-end: url(#arrowhead-gap);
        }

        @keyframes lineAppear {
            to {
                opacity: 0.8;
            }
        }

        /* 箭头标记 */
        .arrow-marker {
            fill: #B3CDE0;
        }

        .arrow-marker-gap {
            fill: #ff6b6b;
        }

        .full-report-btn {
            display: block;
            margin: 2rem auto 0;
            background: linear-gradient(135deg, #A2D9CE 0%, #B3CDE0 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(162, 217, 206, 0.3);
        }

        .full-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(162, 217, 206, 0.4);
        }

        /* 综合报告 */
        .full-report {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 3rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 1600px;
            margin: 0 auto;
        }

        .report-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 2rem;
            gap: 1rem;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn.active {
            color: #B3CDE0;
            background: rgba(179, 205, 224, 0.1);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #B3CDE0;
        }

        .tab-panel {
            display: none;
            animation: fadeIn 0.5s ease;
            min-height: 400px;
        }

        .tab-panel.active {
            display: block;
        }

        .tab-panel.loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }

        .loading-content {
            text-align: center;
            color: #666;
        }

        .loading-spinner {
            display: inline-flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .loading-spinner span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #B3CDE0;
            animation: pulse 1.5s infinite;
        }

        .loading-spinner span:nth-child(2) {
            animation-delay: 0.3s;
        }

        .loading-spinner span:nth-child(3) {
            animation-delay: 0.6s;
        }

        .report-content {
            line-height: 1.8;
            color: #333;
        }

        .report-content h4 {
            color: #B3CDE0;
            font-size: 1.3rem;
            margin: 2rem 0 1rem 0;
            font-weight: 500;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .statute-item, .case-item {
            background: rgba(179, 205, 224, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #B3CDE0;
        }

        .statute-item h5, .case-item h5 {
            color: #333;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .document-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            font-family: 'Noto Sans SC', serif;
        }

        .document-header {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 1rem;
        }

        .document-header h3 {
            font-size: 2rem;
            color: #333;
            font-weight: 700;
        }

        .download-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
            margin-top: 2rem;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 返回按钮样式 */
        .back-btn {
            background: rgba(255, 255, 255, 0.9);
            color: #666;
            border: 2px solid #e0e0e0;
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-btn:hover {
            background: rgba(179, 205, 224, 0.1);
            border-color: #B3CDE0;
            color: #B3CDE0;
            transform: translateX(-3px);
        }

        /* 调试面板 */
        .debug-panel {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 9999;
        }

        .debug-panel button {
            margin-top: 5px;
            padding: 5px 10px;
            background: #333;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        /* 模拟法庭样式 */
        .court-simulation-container {
            max-width: 1000px;
            margin: 0 auto;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .court-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #dee2e6;
        }

        .court-title h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .court-subtitle {
            margin: 0.5rem 0 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .court-status {
            display: flex;
            align-items: center;
        }

        .status-indicator {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .court-participants {
            display: flex;
            justify-content: space-around;
            margin-bottom: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .participant {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .participant .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            transition: transform 0.3s ease;
        }

        .participant.judge .avatar {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        }

        .participant.plaintiff .avatar {
            background: linear-gradient(135deg, #00b894, #00cec9);
        }

        .participant.defendant .avatar {
            background: linear-gradient(135deg, #e17055, #fd79a8);
        }

        .participant span {
            font-weight: 500;
            color: #2c3e50;
        }

        .court-dialogue {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .dialogue-message {
            display: flex;
            margin-bottom: 1.5rem;
            opacity: 0;
            transform: translateY(20px);
            animation: messageSlideIn 0.5s ease forwards;
        }

        @keyframes messageSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dialogue-message.judge {
            justify-content: center;
        }

        .dialogue-message.plaintiff {
            justify-content: flex-start;
        }

        .dialogue-message.defendant {
            justify-content: flex-end;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin: 0 0.5rem;
            flex-shrink: 0;
        }

        .dialogue-message.judge .message-avatar {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        }

        .dialogue-message.plaintiff .message-avatar {
            background: linear-gradient(135deg, #00b894, #00cec9);
        }

        .dialogue-message.defendant .message-avatar {
            background: linear-gradient(135deg, #e17055, #fd79a8);
        }

        .message-bubble {
            max-width: 70%;
            padding: 1rem 1.5rem;
            border-radius: 20px;
            position: relative;
            word-wrap: break-word;
        }

        .dialogue-message.judge .message-bubble {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #6c5ce7;
            text-align: center;
        }

        .dialogue-message.plaintiff .message-bubble {
            background: linear-gradient(135deg, #d1f2eb, #a3e4d7);
            border-left: 4px solid #00b894;
        }

        .dialogue-message.defendant .message-bubble {
            background: linear-gradient(135deg, #fdeaea, #f8d7da);
            border-right: 4px solid #e17055;
        }

        .message-speaker {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .dialogue-message.judge .message-speaker {
            color: #6c5ce7;
        }

        .dialogue-message.plaintiff .message-speaker {
            color: #00b894;
        }

        .dialogue-message.defendant .message-speaker {
            color: #e17055;
        }

        .message-content {
            line-height: 1.6;
            color: #2c3e50;
        }

        .court-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .court-btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .court-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .court-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .court-btn.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #dee2e6;
        }

        .court-btn.secondary:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .court-summary {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .court-summary h4 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .summary-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .summary-section h5 {
            color: #495057;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .summary-section ul {
            list-style: none;
            padding: 0;
        }

        .summary-section li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f3f4;
            color: #6c757d;
        }

        .summary-section li:last-child {
            border-bottom: none;
        }

        .probability-bar {
            position: relative;
            height: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            margin-top: 1rem;
        }

        .probability-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 15px;
            transition: width 2s ease;
            position: relative;
        }

        .probability-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: 600;
            color: #2c3e50;
            z-index: 2;
        }

        /* 打字机效果 */
        .typing-cursor::after {
            content: '|';
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* 右上角fayi标识 */
        .fayi-watermark {
            position: fixed;
            top: 20px;
            right: 20px;
            font-size: 0.8rem;
            color: #333; /* 修改为黑色 */
            font-weight: 300;
            z-index: 1000;
            pointer-events: none;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        /* 数据概览样式 */
        .data-overview-section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(162, 217, 206, 0.1) 100%);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .overview-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .overview-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 2rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .evidence-ratio .card-icon {
            background: linear-gradient(135deg, #B3CDE0, #A2D9CE);
        }

        .missing-evidence .card-icon {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        }

        .win-rate .card-icon {
            background: linear-gradient(135deg, #F5A623, #ff8c42);
        }

        .case-strength .card-icon {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .card-content {
            flex: 1;
        }

        .card-content h3 {
            font-size: 1rem;
            color: #666;
            margin: 0 0 0.5rem 0;
            font-weight: 500;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin: 0.5rem 0;
            line-height: 1;
        }

        .metric-desc {
            font-size: 0.9rem;
            color: #888;
            margin: 0;
        }

        /* 详细报告样式 */
        .detailed-report-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 3rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
            max-width: 1600px;
            margin: 0 auto 2rem auto;
        }

        .tab-panels {
            margin-top: 2rem;
        }

        .tab-panel {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-panel.active {
            display: block;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .action-buttons .download-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 文档内容样式优化 */
        .document-body {
            line-height: 1.8;
            color: #333;
        }

        .party-info p {
            margin-bottom: 1rem;
            text-indent: 2em;
        }

        .case-cause p {
            margin-bottom: 1.5rem;
            text-align: center;
            font-weight: 500;
        }

        .claims-section, .facts-section, .evidence-section {
            margin-bottom: 2rem;
        }

        .claims-section h4, .facts-section h4, .evidence-section h4 {
            color: #B3CDE0;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .claims-section ol, .evidence-section ol {
            padding-left: 2rem;
        }

        .claims-section li, .evidence-section li {
            margin-bottom: 0.5rem;
        }

        .facts-section h5 {
            color: #666;
            font-size: 1rem;
            margin: 1.5rem 0 0.8rem 0;
            font-weight: 500;
        }

        .facts-section p {
            margin-bottom: 1rem;
            text-indent: 2em;
        }

        .closing-section {
            margin: 2rem 0;
            text-align: left;
        }

        .closing-section p {
            margin-bottom: 0.5rem;
        }

        .attachments-section {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .logo-text {
                font-size: 3rem;
            }

            .fayi-watermark {
                font-size: 0.7rem;
                top: 15px;
                right: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 右上角fayi水印 -->
    <div class="fayi-watermark">fayi</div>

    <!-- 欢迎首页 -->
    <div id="welcomePage" class="page active">
        <div class="welcome-container">
            <!-- 顶部Logo和标语 -->
            <header class="welcome-header">
                <div class="logo">
                    <h1 class="logo-text">法弈</h1>
                    <p class="tagline">洞见法律脉络，预见诉讼未来</p>
                </div>
            </header>

            <!-- 中央功能简介区 -->
            <main class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="32" cy="32" r="4" fill="currentColor"/>
                            <circle cx="16" cy="16" r="3" fill="currentColor"/>
                            <circle cx="48" cy="16" r="3" fill="currentColor"/>
                            <circle cx="16" cy="48" r="3" fill="currentColor"/>
                            <circle cx="48" cy="48" r="3" fill="currentColor"/>
                            <path d="M32 28L19 19M32 28L45 19M32 36L19 45M32 36L45 45" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">案件智能解构</h3>
                    <p class="feature-desc">深入剖析案情，提炼关键要素</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M32 8L40 16H24L32 8Z" fill="currentColor"/>
                            <rect x="20" y="16" width="24" height="32" rx="2" fill="currentColor" opacity="0.7"/>
                            <circle cx="16" cy="32" r="4" fill="currentColor"/>
                            <circle cx="48" cy="32" r="4" fill="currentColor"/>
                            <path d="M20 32H16M44 32H48" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">证据链诊断</h3>
                    <p class="feature-desc">梳理现有证据，预警潜在缺口</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="16" y="12" width="32" height="40" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M24 20H40M24 28H40M24 36H36" stroke="white" stroke-width="2"/>
                            <path d="M32 8L36 12H28L32 8Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">法律与案例指引</h3>
                    <p class="feature-desc">智能匹配法规，精准推荐相似判例</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="20" y="8" width="24" height="48" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M28 16H36M28 24H36M28 32H36M28 40H32" stroke="white" stroke-width="2"/>
                            <path d="M16 20L20 16V24L16 20Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">诉讼文书生成</h3>
                    <p class="feature-desc">一键生成专业、规范的法律文书初稿</p>
                </div>
            </main>

            <!-- 底部体验按钮 -->
            <footer class="welcome-footer">
                <button id="startExperienceBtn" class="start-btn">
                    <span>立即体验</span>
                </button>
            </footer>
        </div>
    </div>

    <!-- 核心体验页 -->
    <div id="experiencePage" class="page">
        <div class="experience-container">
            <!-- 信息输入区 -->
            <section class="input-section">
                <h2 class="section-title">请告诉我们您遇到的问题</h2>
                <p class="section-subtitle">我们将运用AI技术为您提供专业的法律分析和诉讼策略建议</p>
                <div class="input-grid">
                    <div class="input-group">
                        <label for="caseDescription">📋 案情基本情况描述</label>
                        <textarea id="caseDescription" placeholder="请详细描述事情的经过，包括：&#10;• 涉及的人物和时间地点&#10;• 具体发生了什么事情&#10;• 造成了什么损失或影响&#10;• 您认为对方的过错在哪里&#10;&#10;例如：2024年3月我在某电商平台购买了一台笔记本电脑..."></textarea>
                    </div>
                    <div class="input-group">
                        <label for="legalClaim">⚖️ 您的主要诉讼请求</label>
                        <textarea id="legalClaim" placeholder="请明确说明您希望通过诉讼达到什么目的：&#10;• 要求赔偿多少损失&#10;• 要求对方做什么或不做什么&#10;• 其他具体要求&#10;&#10;例如：要求对方退还货款12,888元并赔偿损失3,000元..."></textarea>
                    </div>
                </div>
                <div class="analyze-section">
                    <button id="analyzeBtn" class="analyze-btn">
                        <span>立即分析</span>
                    </button>
                </div>
            </section>

            <!-- 智能分析过程展示区 -->
            <section id="analysisSection" class="analysis-section" style="display: none;">
                <div class="analysis-background">
                    <canvas id="networkCanvas"></canvas>
                    <div class="analysis-content">
                        <div class="analysis-text">
                            <h3 id="analysisTitle">模型分析进度</h3>
                            <div id="analysisStage" class="analysis-stage">正在调用证据生成微调模型...</div>

                            <!-- 进度条 -->
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div id="progressFill" class="progress-fill"></div>
                                </div>
                                <div id="progressText" class="progress-text">0%</div>
                            </div>

                            <div class="progress-dots">
                                <span></span><span></span><span></span>
                            </div>
                        </div>
                        <button id="viewReportBtn" class="view-report-btn" style="display: none;">
                            查看证据链分析报告
                        </button>
                    </div>
                </div>
            </section>

            <!-- 分析成果展示区 -->
            <section id="resultsSection" class="results-section" style="display: none;">
                <!-- 证据链分析 -->
                <div id="evidenceChain" class="evidence-chain">
                    <h3>证据链分析图</h3>
                    <div class="evidence-timeline" id="evidenceTimeline">
                        <div class="evidence-container">
                            <!-- 左侧证据列表 -->
                            <div class="evidence-list" id="evidenceList">
                                <h4>证据详情</h4>
                                <!-- 证据详情列表将通过JavaScript动态生成 -->
                            </div>

                            <!-- 中间证据关系图 -->
                            <div class="evidence-graph" id="evidenceGraph">
                                <!-- 证据节点和连接线将通过JavaScript动态生成 -->
                            </div>

                            <!-- 右侧分析报告 -->
                            <div class="evidence-report" id="evidenceReport">
                                <h4>证据分析报告</h4>
                                <!-- 分析报告将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 法律分析按钮 -->
                <div style="text-align: center; margin-top: 2rem;">
                    <button id="viewLegalInsightsBtn" class="full-report-btn">
                        查看法律分析和案例指引
                    </button>
                </div>
            </section>
        </div>
    </div>

    <!-- 法律分析与案例指引页面 -->
    <div id="legalInsightsPage" class="page">
        <div class="experience-container">
            <section class="results-section">
                <div class="full-report">
                    <!-- 返回按钮 -->
                    <div style="margin-bottom: 2rem;">
                        <button id="backToEvidenceBtn" class="back-btn">
                            ← 返回证据链分析
                        </button>
                    </div>

                    <h2 class="section-title">法律分析与案例指引</h2>

                    <!-- 检索动画区域 -->
                    <div id="searchAnimation" class="analysis-background" style="margin-bottom: 3rem; height: 350px; padding: 3rem 2rem;">
                        <canvas id="searchCanvas"></canvas>
                        <div class="analysis-content" style="padding: 2rem 0;">
                            <div class="analysis-text" style="margin-top: 2rem;">
                                <h3 style="font-size: 1.2rem; line-height: 1.6; margin-bottom: 2rem; white-space: nowrap;">正在检索相关法律条文和案例...</h3>
                                <div class="progress-dots">
                                    <span></span><span></span><span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="legalInsightsContent" class="report-content" style="display: none;">
                        <!-- 法律分析内容将动态加载 -->
                    </div>
                    <button id="viewCourtSimulationBtn" class="full-report-btn" style="display: none;">
                        查看模拟法庭推演
                    </button>
                </div>
            </section>
        </div>
    </div>

    <!-- 模拟法庭推演页面 -->
    <div id="courtSimulationPage" class="page">
        <div class="experience-container">
            <section class="results-section">
                <div class="full-report">
                    <!-- 返回按钮 -->
                    <div style="margin-bottom: 2rem;">
                        <button id="backToLegalInsightsBtn" class="back-btn">
                            ← 返回法律分析与案例指引
                        </button>
                    </div>

                    <h2 class="section-title">模拟法庭推演</h2>
                    <div id="courtSimulationContent" class="report-content">
                        <!-- 模拟法庭内容将动态加载 -->
                    </div>
                    <button id="viewLegalDocumentBtn" class="full-report-btn" style="display: none;">
                        查看完整分析报告与起诉文书
                    </button>
                </div>
            </section>
        </div>
    </div>

    <!-- 完整分析报告和起诉文书页面 -->
    <div id="legalDocumentPage" class="page">
        <div class="experience-container">
            <section class="results-section">
                <div class="full-report">
                    <!-- 返回按钮 -->
                    <div style="margin-bottom: 2rem;">
                        <button id="backToCourtSimulationBtn" class="back-btn">
                            ← 返回模拟法庭推演
                        </button>
                    </div>

                    <h2 class="section-title">完整分析报告与起诉文书</h2>

                    <!-- 数据概览区域 -->
                    <div id="dataOverview" class="data-overview-section">
                        <div class="overview-cards">
                            <div class="overview-card evidence-ratio">
                                <div class="card-icon">📊</div>
                                <div class="card-content">
                                    <h3>证据完整度</h3>
                                    <div class="metric-value">78%</div>
                                    <div class="metric-desc">9项证据中已有7项</div>
                                </div>
                            </div>

                            <div class="overview-card missing-evidence">
                                <div class="card-icon">⚠️</div>
                                <div class="card-content">
                                    <h3>关键缺失</h3>
                                    <div class="metric-value">1项</div>
                                    <div class="metric-desc">第三方质检报告</div>
                                </div>
                            </div>

                            <div class="overview-card win-rate">
                                <div class="card-icon">⚖️</div>
                                <div class="card-content">
                                    <h3>预估胜率</h3>
                                    <div class="metric-value">77%</div>
                                    <div class="metric-desc">证据完整度和论证强度的平均值</div>
                                </div>
                            </div>

                            <div class="overview-card case-strength">
                                <div class="card-icon">💪</div>
                                <div class="card-content">
                                    <h3>论证强度</h3>
                                    <div class="metric-value">75%</div>
                                    <div class="metric-desc">基于法理和证据</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细报告内容 -->
                    <div id="detailedReport" class="detailed-report-section">
                        <div class="report-tabs">
                            <button class="tab-btn active" data-tab="evidence-analysis">证据链分析</button>
                            <button class="tab-btn" data-tab="legal-insights">法律分析</button>
                            <button class="tab-btn" data-tab="court-simulation">模拟推演</button>
                            <button class="tab-btn" data-tab="legal-document">起诉文书</button>
                        </div>

                        <div class="tab-panels">
                            <!-- 证据链分析面板 -->
                            <div id="evidence-analysis" class="tab-panel active">
                                <div id="evidenceAnalysisContent" class="report-content">
                                    <!-- 证据分析内容将动态加载 -->
                                </div>
                            </div>

                            <!-- 法律分析面板 -->
                            <div id="legal-insights" class="tab-panel">
                                <div id="legalInsightsDetailContent" class="report-content">
                                    <!-- 法律分析内容将动态加载 -->
                                </div>
                            </div>

                            <!-- 模拟推演面板 -->
                            <div id="court-simulation" class="tab-panel">
                                <div id="courtSimulationDetailContent" class="report-content">
                                    <!-- 模拟推演内容将动态加载 -->
                                </div>
                            </div>

                            <!-- 起诉文书面板 -->
                            <div id="legal-document" class="tab-panel">
                                <div id="legalDocumentContent" class="report-content">
                                    <!-- 起诉文书内容将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button id="downloadReportBtn" class="download-btn">
                            📄 下载完整报告
                        </button>
                        <button id="downloadDocumentBtn" class="download-btn">
                            📋 下载起诉文书
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- 调试面板 - 已隐藏 -->
    <div id="debugPanel" class="debug-panel" style="display: none;">
        <div id="debugInfo">调试信息：<br></div>
        <button onclick="toggleDebug()">关闭</button>
    </div>

    <script>
        // 调试功能（已简化）
        function log(message) {
            // 只在控制台输出，不显示在页面上
            console.log(message);
        }

        function toggleDebug() {
            const panel = document.getElementById('debugPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 页面切换函数
        function switchToExperiencePage() {
            log('开始切换到体验页面');
            const welcomePage = document.getElementById('welcomePage');
            const experiencePage = document.getElementById('experiencePage');

            if (!welcomePage || !experiencePage) {
                log('错误：找不到页面元素');
                return;
            }

            welcomePage.classList.remove('active');
            experiencePage.classList.add('active');
            log('页面切换完成');

            // 1秒后自动填入示例数据
            setTimeout(() => {
                fillExampleData();
            }, 1000);
        }

        // 填充示例数据
        function fillExampleData() {
            log('开始填充示例数据');
            const caseDescription = document.getElementById('caseDescription');
            const legalClaim = document.getElementById('legalClaim');

            if (!caseDescription || !legalClaim) {
                log('错误：找不到输入框');
                return;
            }

            const sampleCase = "这事儿说起来就让人火大！去年11月份，我和老婆看中了海淀区万柳地区的一套二手房，是个2010年建的小区，房子在15楼，面积126平米，三室两厅。当时房主王某某说急着出手，开价680万，我们觉得地段不错，孩子上学也方便，就决定买了。11月18日我们签了《房屋买卖合同》，约定总价680万，我先付了定金20万，剩下的660万等过户时一次性付清。合同里明确写着，房子要在今年1月31日前完成过户，而且房主保证房子没有任何抵押、查封等权利瑕疵。签完合同后，我们就开始准备贷款材料，还把现在住的房子挂牌出售筹钱。结果到了12月底，我们去房管局查档的时候傻眼了！这套房子居然在去年9月份就被法院查封了！原来房主王某某欠了别人300多万，债权人申请了财产保全。更气人的是，这家伙明知道房子被查封了，还跟我们签合同收定金！";
            const sampleClaim = "我们要求：第一，立即解除房屋买卖合同；第二，王某某退还定金20万元；第三，赔偿我们因为他的欺诈行为造成的损失，包括急售现房的差价损失30万、各种费用支出5万，还有因为房价上涨我们重新购房的额外支出40万，总共75万；第四，支付定金的双倍赔偿40万。加起来一共要赔偿我们135万！";

            // 先输入案情描述，完成后再输入诉讼请求
            typeWriter(caseDescription, sampleCase, 30, () => {
                // 案情描述输入完成后，等待500ms再开始输入诉讼请求
                setTimeout(() => {
                    typeWriter(legalClaim, sampleClaim, 30);
                }, 500);
            });
        }

        // 打字机效果
        function typeWriter(element, text, speed, callback) {
            element.value = '';
            let i = 0;

            function type() {
                if (i < text.length) {
                    element.value += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else if (callback) {
                    // 输入完成后执行回调函数
                    callback();
                }
            }

            type();
        }

        // 分析功能
        function startAnalysis() {
            log('开始分析');
            const caseDescription = document.getElementById('caseDescription').value;
            const legalClaim = document.getElementById('legalClaim').value;

            if (!caseDescription.trim() || !legalClaim.trim()) {
                alert('请填写案情描述和诉讼请求');
                return;
            }

            log('输入验证通过，开始显示分析过程');

            // 显示分析区域
            const analysisSection = document.getElementById('analysisSection');
            analysisSection.style.display = 'block';

            // 滚动到分析区域
            setTimeout(() => {
                analysisSection.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            // 开始网络动画
            startNetworkAnimation();

            // 开始进度条动画
            startProgressAnimation();
        }

        // 进度条动画和阶段更新
        function startProgressAnimation() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const analysisStage = document.getElementById('analysisStage');
            const viewReportBtn = document.getElementById('viewReportBtn');

            const stages = [
                { progress: 0, text: '正在调用证据生成微调模型...', duration: 600 },
                { progress: 15, text: '正在解析案情描述...', duration: 900 },
                { progress: 35, text: '正在识别关键法律要素...', duration: 1000 },
                { progress: 55, text: '正在构建证据关系图...', duration: 1100 },
                { progress: 75, text: '正在分析证据完整性...', duration: 900 },
                { progress: 92, text: '正在生成分析报告...', duration: 700 },
                { progress: 100, text: '分析完成！', duration: 600 }
            ];

            let currentStage = 0;

            function updateStage() {
                if (currentStage < stages.length) {
                    const stage = stages[currentStage];

                    // 更新进度条
                    progressFill.style.width = stage.progress + '%';
                    progressText.textContent = stage.progress + '%';

                    // 更新阶段文本
                    analysisStage.textContent = stage.text;

                    // 如果是最后一个阶段，添加完成样式
                    if (stage.progress === 100) {
                        analysisStage.classList.add('completed');
                    }

                    log(`分析阶段 ${currentStage + 1}: ${stage.text} (${stage.progress}%)`);

                    currentStage++;

                    // 如果是最后一个阶段，显示查看报告按钮
                    if (currentStage === stages.length) {
                        setTimeout(() => {
                            viewReportBtn.style.display = 'block';
                            viewReportBtn.style.animation = 'fadeIn 0.5s ease';
                            log('查看报告按钮已显示');
                        }, stage.duration);
                    } else {
                        // 继续下一个阶段
                        setTimeout(updateStage, stage.duration);
                    }
                }
            }

            // 开始第一个阶段
            updateStage();
        }

        // 网络动画
        function startNetworkAnimation() {
            const canvas = document.getElementById('networkCanvas');
            if (!canvas) {
                log('找不到画布元素');
                return;
            }

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.parentElement.offsetWidth;
            canvas.height = canvas.parentElement.offsetHeight;

            // 节点和连接
            const nodes = [];
            const connections = [];
            const keywords = ['借条', '转账记录', '违约', '张三', '合同', '证据', '质检报告', '客服记录'];

            // 创建节点
            for (let i = 0; i < 8; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    radius: Math.random() * 5 + 3,
                    keyword: keywords[i],
                    alpha: Math.random()
                });
            }

            // 创建连接
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    if (Math.random() < 0.3) {
                        connections.push({ from: i, to: j, alpha: 0 });
                    }
                }
            }

            let animationId;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 更新和绘制连接
                connections.forEach(conn => {
                    const fromNode = nodes[conn.from];
                    const toNode = nodes[conn.to];

                    conn.alpha = Math.min(conn.alpha + 0.01, 0.3);

                    ctx.strokeStyle = `rgba(179, 205, 224, ${conn.alpha})`;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(fromNode.x, fromNode.y);
                    ctx.lineTo(toNode.x, toNode.y);
                    ctx.stroke();
                });

                // 更新和绘制节点
                nodes.forEach(node => {
                    // 更新位置
                    node.x += node.vx;
                    node.y += node.vy;

                    // 边界反弹
                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

                    // 更新透明度
                    node.alpha = Math.sin(Date.now() * 0.003 + node.x * 0.01) * 0.3 + 0.7;

                    // 绘制节点
                    ctx.fillStyle = `rgba(179, 205, 224, ${node.alpha})`;
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制关键词
                    ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
                    ctx.font = '12px Noto Sans SC';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.keyword, node.x, node.y - node.radius - 5);
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();
            log('网络动画已启动');
        }

        // 显示证据链
        function showEvidenceChain() {
            log('显示证据链分析');

            // 显示结果区域
            const resultsSection = document.getElementById('resultsSection');
            resultsSection.style.display = 'block';

            // 滚动到结果区域
            setTimeout(() => {
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            // 生成证据链节点
            generateEvidenceNodes();
        }

        // 生成证据链图
        function generateEvidenceNodes() {
            const graph = document.getElementById('evidenceGraph');
            const list = document.getElementById('evidenceList');
            const report = document.getElementById('evidenceReport');

            graph.innerHTML = '';
            list.innerHTML = '<h4>证据详情</h4>';
            report.innerHTML = '<h4>证据分析报告</h4>';

            // 证据数据，包含位置信息（考虑容器内边距20px，节点宽度120px）
            // 有效绘图区域宽度约为容器宽度-40px（左右各20px内边距）
            const evidenceData = [
                { id: 'contract', label: "房屋买卖合同", type: "evidence_owned", desc: "2024年11月18日签订，约定购买万柳地区房屋", x: 10, y: 60 },
                { id: 'receipt', label: "定金收据", type: "evidence_owned", desc: "支付定金20万元的收据凭证", x: 300, y: 60 },
                { id: 'relationship', label: "建立房屋买卖关系", type: "conclusion", desc: "通过合同和收据证明双方存在房屋买卖合同关系", x: 155, y: 180 },
                { id: 'inquiry', label: "不动产查询结果", type: "evidence_owned", desc: "北京市不动产登记中心出具的房屋查封状态查询", x: 480, y: 100 },
                { id: 'ruling', label: "法院执行裁定书", type: "evidence_owned", desc: "（2024）京0108执保1234号执行裁定书", x: 650, y: 100 },
                { id: 'sealed', label: "房屋存在查封", type: "conclusion", desc: "房屋于2024年9月15日被法院查封至2027年9月14日", x: 565, y: 240 },
                { id: 'communication', label: "微信聊天记录", type: "evidence_owned", desc: "与被告的沟通记录，证明其知情和推诿", x: 10, y: 320 },
                { id: 'fraud_evidence', label: "被告欺诈证据", type: "evidence_gap", desc: "⚠️ 缺失：被告明知查封事实的直接证据", x: 300, y: 320 },
                { id: 'fraud', label: "被告明知查封事实", type: "conclusion", desc: "被告在签约时明知房屋被查封仍故意隐瞒", x: 155, y: 460 },
                { id: 'breach', label: "被告违约", type: "conclusion", desc: "无法按期过户构成根本违约", x: 565, y: 420 },
                { id: 'loss_contract', label: "急售房屋合同", type: "evidence_owned", desc: "原告急售现房的买卖合同及价格评估", x: 10, y: 580 },
                { id: 'expense_receipts', label: "费用支出凭证", type: "evidence_owned", desc: "评估费、律师费、中介费等各项费用发票", x: 300, y: 580 },
                { id: 'price_evidence', label: "房价上涨证据", type: "evidence_gap", desc: "⚠️ 缺失：专业机构出具的房价上涨评估报告", x: 480, y: 580 },
                { id: 'loss', label: "原告损失", type: "conclusion", desc: "急售差价30万+费用5万+房价上涨40万", x: 260, y: 720 },
                { id: 'claim', label: "支持解约及赔偿诉求", type: "final_claim", desc: "基于以上证据支持合同解除、定金双倍返还及损失赔偿", x: 350, y: 860 }
            ];

            // 连接关系
            const connections = [
                { from: 'contract', to: 'relationship' },
                { from: 'receipt', to: 'relationship' },
                { from: 'inquiry', to: 'sealed' },
                { from: 'ruling', to: 'sealed' },
                { from: 'communication', to: 'fraud' },
                { from: 'fraud_evidence', to: 'fraud', type: 'gap' },
                { from: 'sealed', to: 'breach' },
                { from: 'fraud', to: 'breach' },
                { from: 'loss_contract', to: 'loss' },
                { from: 'expense_receipts', to: 'loss' },
                { from: 'price_evidence', to: 'loss', type: 'gap' },
                { from: 'relationship', to: 'claim' },
                { from: 'breach', to: 'claim' },
                { from: 'loss', to: 'claim' }
            ];

            // 创建SVG容器用于连接线和箭头
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.width = '100%';
            svg.style.height = '100%';
            svg.style.pointerEvents = 'none';
            svg.style.zIndex = '1';

            // 定义箭头标记
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

            const arrowMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowMarker.setAttribute('id', 'arrowhead');
            arrowMarker.setAttribute('markerWidth', '8');
            arrowMarker.setAttribute('markerHeight', '6');
            arrowMarker.setAttribute('refX', '7');
            arrowMarker.setAttribute('refY', '3');
            arrowMarker.setAttribute('orient', 'auto');

            const arrowPath = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrowPath.setAttribute('points', '0 0, 8 3, 0 6');
            arrowPath.setAttribute('fill', '#B3CDE0');
            arrowMarker.appendChild(arrowPath);
            defs.appendChild(arrowMarker);

            const arrowMarkerGap = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowMarkerGap.setAttribute('id', 'arrowhead-gap');
            arrowMarkerGap.setAttribute('markerWidth', '8');
            arrowMarkerGap.setAttribute('markerHeight', '6');
            arrowMarkerGap.setAttribute('refX', '7');
            arrowMarkerGap.setAttribute('refY', '3');
            arrowMarkerGap.setAttribute('orient', 'auto');

            const arrowPathGap = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrowPathGap.setAttribute('points', '0 0, 8 3, 0 6');
            arrowPathGap.setAttribute('fill', '#ff6b6b');
            arrowMarkerGap.appendChild(arrowPathGap);
            defs.appendChild(arrowMarkerGap);

            svg.appendChild(defs);
            graph.appendChild(svg);

            // 计算连接线的起点和终点（指向节点边框而不是中心）
            function calculateConnectionPoints(fromNode, toNode) {
                const fromCenterX = fromNode.x + 60; // 节点宽度120px的一半
                const fromCenterY = fromNode.y + 37.5; // 节点高度75px的一半
                const toCenterX = toNode.x + 60;
                const toCenterY = toNode.y + 37.5;

                const dx = toCenterX - fromCenterX;
                const dy = toCenterY - fromCenterY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // 根据节点类型计算连接半径
                function getNodeRadius(node) {
                    if (node.type === 'evidence_gap') {
                        // 圆形节点，使用圆形半径
                        return Math.min(60, 37.5); // 取宽高的较小值作为半径
                    } else if (node.type === 'conclusion') {
                        // 圆形节点，使用圆形半径（120px直径，60px半径）
                        return 60;
                    } else if (node.type === 'final_claim') {
                        // 圆形节点，使用圆形半径（120px直径，60px半径）
                        return 60;
                    } else {
                        // 矩形节点，根据角度计算
                        const angle = Math.atan2(dy, dx);
                        const absAngle = Math.abs(angle);
                        if (absAngle < Math.atan2(75, 120)) {
                            return 60; // 连接到左右边
                        } else {
                            return 37.5; // 连接到上下边
                        }
                    }
                }

                const fromRadius = getNodeRadius(fromNode);
                const toRadius = getNodeRadius(toNode);

                const fromX = fromCenterX + (dx / distance) * fromRadius;
                const fromY = fromCenterY + (dy / distance) * fromRadius;
                const toX = toCenterX - (dx / distance) * toRadius;
                const toY = toCenterY - (dy / distance) * toRadius;

                return { fromX, fromY, toX, toY };
            }

            // 创建连接线
            connections.forEach((conn, index) => {
                const fromNode = evidenceData.find(n => n.id === conn.from);
                const toNode = evidenceData.find(n => n.id === conn.to);

                if (fromNode && toNode) {
                    const points = calculateConnectionPoints(fromNode, toNode);

                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', points.fromX);
                    line.setAttribute('y1', points.fromY);
                    line.setAttribute('x2', points.toX);
                    line.setAttribute('y2', points.toY);
                    line.style.stroke = conn.type === 'gap' ? '#ff6b6b' : '#B3CDE0';
                    line.style.strokeWidth = '3';
                    line.style.opacity = '0';
                    line.style.animation = `lineAppear 1s ease ${index * 0.3}s forwards`;
                    line.setAttribute('marker-end', conn.type === 'gap' ? 'url(#arrowhead-gap)' : 'url(#arrowhead)');

                    if (conn.type === 'gap') {
                        line.style.strokeDasharray = '8,4';
                    }

                    svg.appendChild(line);
                }
            });

            // 创建证据节点
            evidenceData.forEach((node, index) => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `evidence-node ${node.type}`;
                nodeElement.style.left = `${node.x}px`;
                nodeElement.style.top = `${node.y}px`;
                nodeElement.style.animationDelay = `${index * 0.2}s`;
                nodeElement.style.zIndex = '2';

                nodeElement.innerHTML = `<h4>${node.label}</h4>`;
                graph.appendChild(nodeElement);

                // 创建左侧列表项
                const listItem = document.createElement('div');
                listItem.className = `evidence-list-item ${node.type}`;
                listItem.innerHTML = `
                    <h5>${node.label}</h5>
                    <p>${node.desc}</p>
                `;
                list.appendChild(listItem);
            });

            // 生成右侧分析报告
            generateEvidenceReport(report);

            log('证据链图生成完成');
        }

        // 生成证据分析报告
        function generateEvidenceReport(reportContainer) {
            const reportContent = `
                <div class="report-section">
                    <h5>证据完整性评估</h5>
                    <p>当前证据链完整度：<strong>78%</strong></p>
                    <div class="report-highlight">
                        <p><strong>优势证据：</strong>合同关系明确，房屋查封事实清楚，损失证明较为完整。</p>
                    </div>
                    <div class="report-highlight">
                        <p><strong>关键缺口：</strong>缺少被告明知查封的直接证据和房价上涨的权威评估报告。</p>
                    </div>
                </div>

                <div class="report-section">
                    <h5>证据链逻辑分析</h5>
                    <p>1. <strong>合同关系确立</strong>：买卖合同+定金收据 → 房屋买卖关系成立</p>
                    <p>2. <strong>查封事实认定</strong>：不动产查询+执行裁定书 → 房屋被司法查封</p>
                    <p>3. <strong>欺诈行为认定</strong>：查封时间+签约时间 → 被告明知而隐瞒</p>
                    <p>4. <strong>违约责任确定</strong>：无法过户+拒绝退款 → 被告根本违约</p>
                    <p>5. <strong>损失因果关系</strong>：信赖合同+急售现房 → 直接经济损失</p>
                </div>

                <div class="report-section">
                    <h5>补强建议</h5>
                    <p>• 申请法院调取被告在执行案件中的相关材料</p>
                    <p>• 委托专业房地产评估机构出具市场价格分析报告</p>
                    <p>• 整理完善与被告的所有沟通记录</p>
                    <p>• 收集同期同地段房屋交易价格信息</p>
                </div>

                <div class="report-section">
                    <h5>胜诉概率预测</h5>
                    <p>基于当前证据：<strong style="color: #A2D9CE;">80-85%</strong></p>
                    <p>补强证据后：<strong style="color: #A2D9CE;">90-95%</strong></p>
                </div>
            `;

            reportContainer.innerHTML += reportContent;
        }

        // 显示法律分析页面
        function showLegalInsights() {
            log('显示法律分析页面');
            switchToPage('legalInsightsPage');
            startLegalAnalysisAnimation();
        }

        // 显示模拟法庭页面
        function showCourtSimulation() {
            log('显示模拟法庭页面');
            switchToPage('courtSimulationPage');
            loadCourtSimulationContent();
        }

        // 显示完整分析报告和起诉文书页面
        function showLegalDocument() {
            log('显示完整分析报告页面');
            switchToPage('legalDocumentPage');
            loadCompleteReport();
        }

        // 加载完整报告
        function loadCompleteReport() {
            log('开始加载完整报告');

            // 加载各个模块的内容
            loadEvidenceAnalysisDetail();
            loadLegalInsightsDetail();
            loadCourtSimulationDetail();
            loadLegalDocumentDetail();

            // 初始化标签页切换
            setTimeout(() => {
                initReportTabs();
            }, 100);
        }

        // 初始化报告标签页
        function initReportTabs() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const targetTab = btn.getAttribute('data-tab');

                    // 移除所有活动状态
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabPanels.forEach(p => p.classList.remove('active'));

                    // 激活当前标签
                    btn.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }

        // 加载证据分析详情
        function loadEvidenceAnalysisDetail() {
            const container = document.getElementById('evidenceAnalysisContent');
            if (!container) return;

            container.innerHTML = `
                <h4>证据链完整性分析</h4>
                <div class="report-highlight">
                    <strong>证据完整度：78%</strong> - 当前已收集到关键证据7项，仍缺失2项核心证据
                </div>

                <h5>已有证据清单</h5>
                <ul>
                    <li><strong>房屋买卖合同：</strong>证明双方合同关系及约定内容，签订时间2024年11月18日</li>
                    <li><strong>定金收据：</strong>证明已支付定金200,000元</li>
                    <li><strong>不动产查询结果：</strong>证明房屋查封状态及查封时间（2024年9月15日）</li>
                    <li><strong>法院执行裁定书：</strong>证明查封的法律依据和具体原因</li>
                    <li><strong>微信聊天记录：</strong>证明与被告的沟通过程和其推诿态度</li>
                    <li><strong>损失证明材料：</strong>急售现房合同、各项费用支出凭证</li>
                </ul>

                <h5>关键证据缺口</h5>
                <div class="report-highlight" style="border-left-color: #ff6b6b; background: rgba(255, 107, 107, 0.1);">
                    <strong>1. 被告明知查封的直接证据：</strong>虽有间接证据，但缺乏被告在签约前已知晓查封情况的直接证明。建议申请法院调取相关执行卷宗，获取被告收到查封通知书的时间证据。
                </div>
                <div class="report-highlight" style="border-left-color: #ff6b6b; background: rgba(255, 107, 107, 0.1);">
                    <strong>2. 房价上涨的权威证据：</strong>缺乏专业机构出具的房地产市场价格评估报告。建议委托具有资质的房地产评估机构出具正式的市场价格分析报告。
                </div>

                <h5>证据补强建议</h5>
                <ol>
                    <li>申请法院调取被告在执行案件中的相关材料</li>
                    <li>委托专业房地产评估机构出具市场价格分析报告</li>
                    <li>整理完善与被告的所有沟通记录</li>
                    <li>收集同期同地段房屋交易价格信息作为辅助证据</li>
                </ol>
            `;
        }

        // 加载法律分析详情
        function loadLegalInsightsDetail() {
            const container = document.getElementById('legalInsightsDetailContent');
            if (!container) return;

            container.innerHTML = `
                <h4>适用法律条文</h4>
                <div class="statute-item">
                    <h5>《中华人民共和国民法典》第一百四十八条</h5>
                    <p>一方以欺诈手段，使对方在违背真实意思的情况下实施的民事法律行为，受欺诈方有权请求人民法院或者仲裁机构予以撤销。</p>
                </div>

                <div class="statute-item">
                    <h5>《中华人民共和国民法典》第五百六十三条</h5>
                    <p>有下列情形之一的，当事人可以解除合同：（一）因不可抗力致使不能实现合同目的；（二）在履行期限届满前，当事人一方明确表示或者以自己的行为表明不履行主要债务；（三）当事人一方迟延履行主要债务，经催告后在合理期限内仍未履行；（四）当事人一方迟延履行债务或者有其他违约行为致使不能实现合同目的。</p>
                </div>

                <div class="statute-item">
                    <h5>《中华人民共和国民法典》第五百八十七条</h5>
                    <p>债务人履行债务的，定金应当抵作价款或者收回。给付定金的一方不履行债务或者履行债务不符合约定，致使不能实现合同目的的，无权请求返还定金；收受定金的一方不履行债务或者履行债务不符合约定，致使不能实现合同目的的，应当双倍返还定金。</p>
                </div>

                <h4>相关指导案例</h4>
                <div class="case-item">
                    <h5>张某诉李某房屋买卖合同纠纷案</h5>
                    <p><strong>案号：</strong>（2023）京0108民初15678号</p>
                    <p><strong>案情概要：</strong>原告张某与被告李某签订房屋买卖合同，约定购买海淀区住房，成交价格580万元，定金50万元。后发现房屋早在签约前即被法院查封，被告明知此情况却故意隐瞒。原告要求解除合同并双倍返还定金。</p>
                    <p><strong>争议焦点：</strong>①出卖人隐瞒房屋查封事实是否构成欺诈；②买受人是否有权解除合同并要求双倍返还定金；③因房价变动导致的损失如何认定。</p>
                    <p><strong>法院认定：</strong>①出卖人明知房屋存在查封等权利限制而故意隐瞒，构成欺诈；②买受人有权解除合同并要求双倍返还定金；③损失赔偿应以实际发生的合理损失为准，包括必要费用和房价变动损失。</p>
                    <p><strong>判决结果：</strong>确认合同解除，被告双倍返还定金100万元，赔偿房价上涨损失35万元。</p>
                    <p><strong>借鉴价值：</strong>明确了房屋买卖中出卖人的如实告知义务，隐瞒重大事实构成欺诈的认定标准。</p>
                </div>

                <div class="case-item">
                    <h5>王某某诉赵某某房屋买卖合同纠纷案</h5>
                    <p><strong>案号：</strong>（2024）京0105民初9876号</p>
                    <p><strong>案情概要：</strong>原告王某某与被告赵某某签订房屋买卖合同，约定购买朝阳区住房，总价720万元，定金80万元。合同签订后发现房屋存在抵押且被告未告知。被告辩称正在办理解押，要求继续履行合同。</p>
                    <p><strong>关键证据：</strong>①房屋买卖合同原件；②不动产登记中心查询结果；③银行抵押登记记录；④双方沟通记录；⑤房地产评估机构出具的市场价格分析报告。</p>
                    <p><strong>法院观点：</strong>房屋存在权利限制期间，出卖人无法履行过户义务，构成根本违约。买受人因信赖合同履行而产生的合理支出和机会成本损失，应由违约方承担。损失数额需要充分证据支持，特别是房价变动损失需要专业评估机构意见。</p>
                    <p><strong>判决结果：</strong>确认合同解除，被告返还定金80万元并支付同等数额定金罚则，赔偿合理损失28万元。</p>
                    <p><strong>指导意义：</strong>确立了房屋买卖中权利瑕疵的认定标准和损失赔偿的计算方法。</p>
                </div>

                <h4>法律风险评估</h4>
                <div class="report-section">
                    <h5>有利因素</h5>
                    <ul>
                        <li>房屋查封事实清楚，时间节点明确，被告明知而隐瞒构成欺诈</li>
                        <li>合同关系明确，定金支付有据，符合定金罚则适用条件</li>
                        <li>被告根本违约，无法履行过户义务，合同目的无法实现</li>
                        <li>有完整的损失证明材料，因果关系清晰</li>
                    </ul>
                </div>

                <div class="report-section">
                    <h5>不利因素</h5>
                    <ul>
                        <li>缺乏被告明知查封事实的直接证据</li>
                        <li>房价上涨损失缺乏权威评估机构的专业报告</li>
                        <li>被告可能抗辩称正在积极解决查封问题</li>
                        <li>部分损失数额的合理性可能受到质疑</li>
                    </ul>
                </div>
            `;
        }

        // 加载模拟推演详情
        function loadCourtSimulationDetail() {
            const container = document.getElementById('courtSimulationDetailContent');
            if (!container) return;

            container.innerHTML = `
                <h4>争议焦点分析</h4>
                <div class="report-section">
                    <h5>🎯 主要争议点</h5>
                    <ol>
                        <li><strong>欺诈行为的认定：</strong>被告是否明知房屋查封事实而故意隐瞒</li>
                        <li><strong>合同解除的条件：</strong>是否符合法定合同解除条件</li>
                        <li><strong>定金罚则的适用：</strong>是否符合"致使不能实现合同目的"的标准</li>
                        <li><strong>损失赔偿的合理性：</strong>各项损失数额及因果关系的认定</li>
                    </ol>
                </div>

                <h4>双方可能的主张和抗辩</h4>
                <div class="report-section">
                    <h5>💪 我方优势论点</h5>
                    <ul>
                        <li>被告明知房屋查封仍签约收取定金，构成合同欺诈，应承担相应法律后果</li>
                        <li>房屋查封导致无法过户，被告构成根本违约，原告有权解除合同</li>
                        <li>原告为履行合同承担的各项损失均有合理依据，应由违约方全额赔偿</li>
                        <li>适用定金罚则，被告应双倍返还定金20万元</li>
                    </ul>
                </div>

                <div class="report-section">
                    <h5>⚠️ 对方可能抗辩</h5>
                    <ul>
                        <li>被告可能抗辩称其对查封事实不知情，或声称正在积极解决查封问题</li>
                        <li>被告可能质疑原告损失数额的真实性和合理性，特别是房价上涨损失的计算依据</li>
                        <li>被告可能主张原告未给予合理的履行宽限期，或者原告存在其他违约行为</li>
                        <li>被告可能争议定金罚则的适用条件，认为不符合"致使不能实现合同目的"的标准</li>
                    </ul>
                </div>

                <h4>应对策略建议</h4>
                <div class="report-section">
                    <h5>🎯 核心策略</h5>
                    <ol>
                        <li><strong>申请法院调取执行材料：</strong>申请法院调取被告在执行案件中的相关材料，证明其对查封事实的明确知情，强化欺诈认定的事实基础</li>
                        <li><strong>委托专业评估机构：</strong>委托专业房地产评估机构出具市场价格分析报告，为房价上涨损失提供权威的数据支撑</li>
                        <li><strong>整理完善沟通记录：</strong>整理完善与被告的所有沟通记录，特别是被告承认查封事实或拒绝承担责任的相关证据</li>
                        <li><strong>准备反驳预案：</strong>准备应对被告可能提出的反驳，如收集同期其他房屋交易的价格信息，证明市场价格变动的普遍性和客观性</li>
                    </ol>
                </div>

                <h4>胜诉概率评估</h4>
                <div class="probability-bar">
                    <div class="probability-fill" style="width: 85%;">
                        <div class="probability-text">85%</div>
                    </div>
                </div>
                <p style="margin-top: 1rem; color: #666;">基于现有证据和类似案例分析，预估胜诉概率为85%。如能补强关键证据，胜诉概率可提升至90%以上。</p>
            `;
        }

        // 加载起诉文书详情
        function loadLegalDocumentDetail() {
            const container = document.getElementById('legalDocumentContent');
            if (!container) return;

            container.innerHTML = `
                <div class="document-content">
                    <div class="document-header">
                        <h3>民事起诉状</h3>
                    </div>

                    <div class="document-body">
                        <div class="party-info">
                            <p><strong>原告：</strong>李明，男，1985年3月15日出生，汉族，身份证号：11010819850315XXXX，住址：北京市海淀区中关村大街XX号院X号楼X单元XXX室，联系电话：138-0010-8888。</p>

                            <p><strong>原告：</strong>张丽，女，1987年7月22日出生，汉族，身份证号：11010819870722XXXX，住址：北京市海淀区中关村大街XX号院X号楼X单元XXX室，联系电话：139-0010-9999。</p>

                            <p><strong>被告：</strong>王建国，男，1975年12月8日出生，汉族，身份证号：11010819751208XXXX，住址：北京市海淀区万柳东路XX号院X号楼X单元XXX室，联系电话：136-0010-7777。</p>
                        </div>

                        <div class="case-cause">
                            <p><strong>案由：</strong>房屋买卖合同纠纷</p>
                        </div>

                        <div class="claims-section">
                            <h4>诉讼请求</h4>
                            <ol>
                                <li>确认原告与被告于2024年11月18日签订的《房屋买卖合同》解除；</li>
                                <li>判令被告立即退还原告定金人民币200,000元；</li>
                                <li>判令被告向原告支付定金罚则人民币200,000元；</li>
                                <li>判令被告赔偿原告因合同解除造成的损失人民币750,000元，其中包括：急售现房差价损失300,000元、各项费用支出50,000元、重新购房额外支出400,000元；</li>
                                <li>判令被告承担本案全部诉讼费用。</li>
                            </ol>
                        </div>

                        <div class="facts-section">
                            <h4>事实与理由</h4>

                            <h5>一、合同签订及履行情况</h5>
                            <p>2024年11月18日，原告李明、张丽（系夫妻关系）与被告王建国签订《房屋买卖合同》，约定购买被告位于北京市海淀区万柳东路XX号院X号楼X单元XXX室的房屋，建筑面积126平方米，成交价格为人民币6,800,000元。合同约定原告支付定金200,000元，剩余房款6,600,000元在办理过户手续时一次性支付。合同明确约定过户时间为2025年1月31日前，被告保证该房屋权属清晰，无抵押、查封等权利限制。合同签订当日，原告依约支付定金200,000元。</p>

                            <h5>二、被告隐瞒房屋查封事实，构成欺诈</h5>
                            <p><strong>1. 房屋早已被司法查封，被告明知而故意隐瞒：</strong></p>
                            <p>经原告于2024年12月底向北京市不动产登记中心查询发现，涉案房屋早在2024年9月15日即被北京市海淀区人民法院依据（2024）京0108执保1234号执行裁定书予以查封，查封期限至2027年9月14日。该查封系因被告王建国作为被执行人，在李某某与王建国民间借贷纠纷案中未履行生效判决确定的给付义务（本金3,200,000元及相应利息），债权人李某某申请强制执行所致。被告在签订合同时对此查封事实完全知情，却故意向原告隐瞒，并在合同中明确承诺房屋无任何权利限制，其行为构成典型的合同欺诈。</p>

                            <p><strong>2. 被告的欺诈行为导致合同目的无法实现：</strong></p>
                            <p>由于房屋处于司法查封状态，根据《最高人民法院关于人民法院民事执行中查封、扣押、冻结财产的规定》，查封期间不得办理过户登记手续。这意味着无论原告如何履行付款义务，被告都无法按约定时间完成过户，合同目的根本无法实现。被告明知此种情况仍与原告签约并收取定金，主观恶意明显。</p>

                            <h5>三、被告违约在先且拒绝承担责任</h5>
                            <p><strong>1. 被告根本违约，合同解除条件成就：</strong></p>
                            <p>合同约定的过户期限为2025年1月31日，但直至该期限届满，被告仍无法提供符合过户条件的房屋。原告多次催促，被告仅以'正在想办法解除查封'等理由搪塞，从未提出具体可行的解决方案。被告的行为构成根本违约，严重影响合同目的实现，原告有权依法解除合同。</p>

                            <p><strong>2. 被告拒绝承担违约责任，态度恶劣：</strong></p>
                            <p>面对原告合理的解约要求，被告不仅拒绝退还定金，反而倒打一耙，声称系原告违约在先。被告完全无视自身的欺诈行为和违约责任，拒绝承担任何损失赔偿，迫使原告不得不通过诉讼途径维护合法权益。</p>
                        </div>

                        <div class="evidence-section">
                            <h4>证据清单及来源</h4>
                            <ol>
                                <li>房屋买卖合同原件及被告收取定金的收据，用以证明双方合同关系及约定内容；</li>
                                <li>北京市不动产登记中心出具的房屋查询结果，证明涉案房屋的查封状态及查封时间；</li>
                                <li>北京市海淀区人民法院（2024）京0108执保1234号执行裁定书，证明房屋查封的具体原因和法律依据；</li>
                                <li>原告与被告的微信聊天记录、通话录音等，证明被告对查封事实的知情以及拒绝承担责任的态度；</li>
                                <li>原告急售现房的买卖合同及相关价格评估报告，证明差价损失的具体数额；</li>
                                <li>各项费用支出的发票和收据，包括评估费、律师费、中介费等；</li>
                                <li>同地段同类房屋的市场价格调研报告，证明房价上涨导致的额外购房成本。</li>
                            </ol>
                        </div>

                        <div class="closing-section">
                            <p>此致</p>
                            <p><strong>北京市海淀区人民法院</strong></p>
                            <br>
                            <p style="text-align: right;">起诉人：李明（手写签名）</p>
                            <p style="text-align: right;">起诉人：张丽（手写签名）</p>
                            <p style="text-align: right;">二〇二五年二月十五日</p>
                        </div>

                        <div class="attachments-section">
                            <h4>附项</h4>
                            <ol>
                                <li>本起诉状副本1份；</li>
                                <li>证据材料复印件1套；</li>
                                <li>原告身份证复印件各1份；</li>
                                <li>房屋买卖合同复印件1份。</li>
                            </ol>
                        </div>
                    </div>
                </div>
            `;
        }

        // 页面切换通用函数
        function switchToPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                // 滚动到页面顶部
                setTimeout(() => {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }, 100);
            }
        }

        // 返回证据链分析页面
        function backToEvidenceChain() {
            log('返回证据链分析页面');
            switchToPage('experiencePage');

            // 确保证据链分析结果是显示的
            const resultsSection = document.getElementById('resultsSection');
            if (resultsSection) {
                resultsSection.style.display = 'block';
                // 滚动到证据链分析区域
                setTimeout(() => {
                    resultsSection.scrollIntoView({ behavior: 'smooth' });
                }, 200);
            }
        }

        // 返回法律分析与案例指引页面
        function backToLegalInsights() {
            log('返回法律分析与案例指引页面');
            switchToPage('legalInsightsPage');
        }

        // 返回模拟法庭推演页面
        function backToCourtSimulation() {
            log('返回模拟法庭推演页面');
            switchToPage('courtSimulationPage');
        }

        // 开始法律分析动画
        function startLegalAnalysisAnimation() {
            log('开始法律分析动画');

            // 显示检索动画
            const searchAnimation = document.getElementById('searchAnimation');
            const legalContent = document.getElementById('legalInsightsContent');
            const nextButton = document.getElementById('viewCourtSimulationBtn');

            if (searchAnimation) {
                searchAnimation.style.display = 'block';
                startSearchAnimation();
            }

            // 3秒后显示第一部分内容（法律条文）
            setTimeout(() => {
                loadLegalAnalysisFirstPart();
                legalContent.style.display = 'block';
                searchAnimation.style.display = 'none';

                // 立即显示第二部分内容（案例检索动画）
                loadLegalAnalysisSecondPart();
            }, 3000);
        }

        // 检索动画
        function startSearchAnimation() {
            const canvas = document.getElementById('searchCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.parentElement.offsetWidth;
            canvas.height = canvas.parentElement.offsetHeight;

            // 创建法律条文关键词节点
            const legalKeywords = ['消费者权益保护法', '民法典', '产品质量法', '违约责任', '三包规定', '举证责任'];
            const nodes = [];

            for (let i = 0; i < legalKeywords.length; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 1.5,
                    vy: (Math.random() - 0.5) * 1.5,
                    radius: Math.random() * 4 + 4,
                    keyword: legalKeywords[i],
                    alpha: Math.random() * 0.5 + 0.5,
                    color: `hsl(${200 + Math.random() * 60}, 70%, 60%)`
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                nodes.forEach(node => {
                    // 更新位置
                    node.x += node.vx;
                    node.y += node.vy;

                    // 边界反弹
                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

                    // 更新透明度
                    node.alpha = Math.sin(Date.now() * 0.002 + node.x * 0.01) * 0.3 + 0.7;

                    // 绘制节点
                    ctx.fillStyle = node.color.replace(')', `, ${node.alpha})`).replace('hsl', 'hsla');
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制关键词
                    ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
                    ctx.font = '11px Noto Sans SC';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.keyword, node.x, node.y - node.radius - 8);
                });

                // 绘制连接线
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        const dx = nodes[j].x - nodes[i].x;
                        const dy = nodes[j].y - nodes[i].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 150) {
                            const opacity = (150 - distance) / 150 * 0.3;
                            ctx.strokeStyle = `rgba(179, 205, 224, ${opacity})`;
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(nodes[i].x, nodes[i].y);
                            ctx.lineTo(nodes[j].x, nodes[j].y);
                            ctx.stroke();
                        }
                    }
                }

                requestAnimationFrame(animate);
            }

            animate();
        }

        // 加载法律分析第一部分（法律条文）
        function loadLegalAnalysisFirstPart() {
            const container = document.getElementById('legalInsightsContent');
            if (!container) return;

            container.innerHTML = `
                <div style="animation: fadeIn 0.8s ease;">
                    <h4>相关法律条文</h4>
                    <div class="statute-item">
                        <h5>《中华人民共和国民法典》第一百四十八条</h5>
                        <p>一方以欺诈手段，使对方在违背真实意思的情况下实施的民事法律行为，受欺诈方有权请求人民法院或者仲裁机构予以撤销。</p>
                    </div>
                    <div class="statute-item">
                        <h5>《中华人民共和国民法典》第五百六十三条</h5>
                        <p>有下列情形之一的，当事人可以解除合同：（一）因不可抗力致使不能实现合同目的；（二）在履行期限届满前，当事人一方明确表示或者以自己的行为表明不履行主要债务；（三）当事人一方迟延履行主要债务，经催告后在合理期限内仍未履行；（四）当事人一方迟延履行债务或者有其他违约行为致使不能实现合同目的；（五）法律规定的其他情形。</p>
                    </div>
                    <div class="statute-item">
                        <h5>《中华人民共和国民法典》第五百八十七条</h5>
                        <p>债务人履行债务的，定金应当抵作价款或者收回。给付定金的一方不履行债务或者履行债务不符合约定，致使不能实现合同目的的，无权请求返还定金；收受定金的一方不履行债务或者履行债务不符合约定，致使不能实现合同目的的，应当双倍返还定金。</p>
                    </div>
                    <div class="statute-item">
                        <h5>《中华人民共和国民法典》第五百七十七条</h5>
                        <p>当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担继续履行、采取补救措施或者赔偿损失等违约责任。</p>
                    </div>
                </div>
                <div id="secondPartContent" style="display: none;">
                    <!-- 案例检索动画区域 -->
                    <div id="caseSearchAnimation" class="analysis-background" style="margin-top: 1rem; margin-bottom: 2rem; height: 300px; padding: 2rem;">
                        <canvas id="caseSearchCanvas"></canvas>
                    </div>
                    <div id="caseContent" style="display: none;">
                        <!-- 案例内容将在检索完成后显示 -->
                    </div>
                </div>
            `;
        }

        // 加载法律分析第二部分（显示案例检索动画）
        function loadLegalAnalysisSecondPart() {
            const secondPart = document.getElementById('secondPartContent');
            if (!secondPart) return;

            // 显示第二部分容器和案例检索动画
            secondPart.style.display = 'block';
            secondPart.style.animation = 'fadeIn 0.8s ease';

            // 开始案例检索动画
            startCaseSearchAnimation();

            // 3秒后显示案例内容
            setTimeout(() => {
                loadCaseAnalysisContent();
            }, 3000);
        }

        // 案例检索动画
        function startCaseSearchAnimation() {
            const canvas = document.getElementById('caseSearchCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.parentElement.offsetWidth;
            canvas.height = canvas.parentElement.offsetHeight;

            // 创建案例相关关键词节点
            const caseKeywords = ['产品责任纠纷', '网络购物合同', '消费者胜诉', '举证标准', '三包义务', '质量缺陷'];
            const nodes = [];

            for (let i = 0; i < caseKeywords.length; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 1.2,
                    vy: (Math.random() - 0.5) * 1.2,
                    radius: Math.random() * 4 + 5,
                    keyword: caseKeywords[i],
                    alpha: Math.random() * 0.5 + 0.5,
                    color: `hsl(${30 + Math.random() * 40}, 75%, 65%)` // 橙色系，区别于法条的蓝色系
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                nodes.forEach(node => {
                    // 更新位置
                    node.x += node.vx;
                    node.y += node.vy;

                    // 边界反弹
                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

                    // 更新透明度
                    node.alpha = Math.sin(Date.now() * 0.0025 + node.x * 0.008) * 0.3 + 0.7;

                    // 绘制节点
                    ctx.fillStyle = node.color.replace(')', `, ${node.alpha})`).replace('hsl', 'hsla');
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制关键词
                    ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
                    ctx.font = '11px Noto Sans SC';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.keyword, node.x, node.y - node.radius - 8);
                });

                // 绘制连接线
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        const dx = nodes[j].x - nodes[i].x;
                        const dy = nodes[j].y - nodes[i].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 140) {
                            const opacity = (140 - distance) / 140 * 0.25;
                            ctx.strokeStyle = `rgba(245, 166, 35, ${opacity})`;
                            ctx.lineWidth = 1.5;
                            ctx.beginPath();
                            ctx.moveTo(nodes[i].x, nodes[i].y);
                            ctx.lineTo(nodes[j].x, nodes[j].y);
                            ctx.stroke();
                        }
                    }
                }

                requestAnimationFrame(animate);
            }

            animate();
        }

        // 加载案例分析内容
        function loadCaseAnalysisContent() {
            const caseSearchAnimation = document.getElementById('caseSearchAnimation');
            const caseContent = document.getElementById('caseContent');
            const nextButton = document.getElementById('viewCourtSimulationBtn');

            if (caseSearchAnimation) {
                caseSearchAnimation.style.display = 'none';
            }

            if (caseContent) {
                caseContent.innerHTML = `
                    <div style="animation: fadeIn 0.8s ease;">
                        <h4>指导案例</h4>
                        <div class="case-item">
                            <h5>张某诉李某房屋买卖合同纠纷案</h5>
                            <p><strong>案号：</strong>（2023）京0108民初15678号</p>
                            <p><strong>基本案情：</strong>原告张某于2023年5月与被告李某签订房屋买卖合同，约定购买被告位于海淀区的一套住房，成交价格580万元，定金50万元。合同约定过户时间为同年8月31日前。后原告发现该房屋早在2023年3月即被法院查封，被告在签约时明知此情况却故意隐瞒。原告要求解除合同并双倍返还定金，被告拒绝。</p>
                            <p><strong>争议焦点：</strong>①出卖人隐瞒房屋查封事实是否构成欺诈；②买受人是否有权解除合同并要求双倍返还定金；③因房价变动导致的损失如何认定。</p>
                            <p><strong>法院认定：</strong>出卖人在签订合同时明知房屋存在查封等权利限制而故意隐瞒，构成欺诈。买受人有权解除合同并要求双倍返还定金。关于损失赔偿，应以实际发生的合理损失为准，包括为履行合同支出的必要费用和因房价变动导致的差价损失。</p>
                            <p><strong>判决要点：</strong>①确认合同解除；②被告双倍返还定金100万元；③赔偿原告因房价上涨导致的重新购房损失35万元；④诉讼费由被告承担。</p>
                            <p><strong>典型意义：</strong>本案明确了房屋买卖中出卖人的如实告知义务，隐瞒重大事实构成欺诈的，买受人有权撤销合同并要求赔偿损失。</p>
                        </div>

                        <div class="case-item">
                            <h5>王某某诉赵某某房屋买卖合同纠纷案</h5>
                            <p><strong>案号：</strong>（2024）京0105民初9876号</p>
                            <p><strong>基本案情：</strong>原告王某某与被告赵某某签订房屋买卖合同，约定购买朝阳区某住房，总价720万元，定金80万元。合同签订后，原告发现房屋存在抵押且被告未告知。被告辩称正在办理解押手续，要求原告继续履行合同。原告认为被告违约，要求解除合同并赔偿损失。</p>
                            <p><strong>关键证据：</strong>①房屋买卖合同原件；②不动产登记中心查询结果；③银行抵押登记记录；④双方微信聊天记录；⑤房地产评估机构出具的市场价格分析报告。</p>
                            <p><strong>法院观点：</strong>房屋被司法查封期间，出卖人无法履行过户义务，构成根本违约。买受人因信赖合同履行而产生的合理支出和机会成本损失，应由违约方承担。但损失数额的认定需要充分的证据支持，特别是房价变动损失需要专业评估机构的意见。</p>
                            <p><strong>判决结果：</strong>①确认合同解除；②被告返还定金80万元并支付同等数额的定金罚则；③赔偿原告合理损失28万元；④案件受理费由被告负担。</p>
                            <p><strong>指导价值：</strong>确立了房屋买卖中权利瑕疵的认定标准和损失赔偿的计算方法，强调了专业评估在损失认定中的重要作用。</p>
                        </div>

                        <h4>法律分析结论</h4>
                        <div class="report-highlight">
                            <p><strong>胜诉概率评估：</strong>基于现有证据和相关法律条文，本案胜诉概率约为85-90%。</p>
                            <p><strong>关键法律依据：</strong>民法典欺诈撤销、合同解除、定金罚则条款为主要依据，违约责任条款为补充。</p>
                            <p><strong>证据补强建议：</strong>建议申请法院调取执行卷宗并委托专业评估机构出具房价分析报告，将胜诉概率提升至95%以上。</p>
                        </div>
                    </div>
                `;

                caseContent.style.display = 'block';

                // 法律分析完成后显示"查看模拟法庭推演"按钮
                if (nextButton) {
                    nextButton.style.display = 'block';
                    nextButton.style.animation = 'fadeIn 0.5s ease';
                }
            }
        }
        // 加载模拟法庭内容
        function loadCourtSimulationContent() {
            const container = document.getElementById('courtSimulationContent');
            if (!container) return;

            container.innerHTML = `
                <div class="court-simulation-container">
                    <div class="court-header">
                        <div class="court-title">
                            <h4>🏛️ 模拟法庭辩论现场</h4>
                            <p class="court-subtitle">案件：李明、张丽诉王建国房屋买卖合同纠纷案</p>
                        </div>
                        <div class="court-status">
                            <span class="status-indicator">● 庭审进行中</span>
                        </div>
                    </div>

                    <div class="court-participants">
                        <div class="participant plaintiff">
                            <div class="avatar">👨‍💼</div>
                            <span>原告方</span>
                        </div>
                        <div class="participant judge">
                            <div class="avatar">⚖️</div>
                            <span>审判长</span>
                        </div>
                        <div class="participant defendant">
                            <div class="avatar">🏢</div>
                            <span>被告方</span>
                        </div>
                    </div>

                    <div id="courtDialogue" class="court-dialogue">
                        <!-- 对话内容将动态加载 -->
                    </div>

                    <div class="court-controls">
                        <button id="startCourtDebate" class="court-btn primary">开始庭审辩论</button>
                        <button id="pauseCourtDebate" class="court-btn secondary" style="display: none;">暂停</button>
                        <button id="skipCourtDebate" class="court-btn secondary" style="display: none;">中止</button>
                    </div>

                    <div id="courtSummary" class="court-summary" style="display: none;">
                        <h4>庭审总结</h4>
                        <div class="summary-content">
                            <div class="summary-section">
                                <h5>🎯 争议焦点</h5>
                                <ul>
                                    <li>产品故障是否属于质量缺陷</li>
                                    <li>商家是否履行了三包义务</li>
                                    <li>误工损失的计算标准和因果关系</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h5>💡 论证建议</h5>
                                <ul>
                                    <li>尽快获取第三方检测报告，强化产品质量缺陷证明</li>
                                    <li>收集更多商家推诿的证据材料，如录音、聊天记录</li>
                                    <li>完善误工损失的计算依据和相关证明文件</li>
                                    <li>准备同类产品的质量标准对比材料</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h5>📊 论证强度</h5>
                                <div class="probability-bar">
                                    <div class="probability-fill" style="width: 75%"></div>
                                    <span class="probability-text">75%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 绑定庭审控制按钮事件
            setTimeout(() => {
                const startBtn = document.getElementById('startCourtDebate');
                const pauseBtn = document.getElementById('pauseCourtDebate');
                const skipBtn = document.getElementById('skipCourtDebate');

                if (startBtn) {
                    startBtn.addEventListener('click', startCourtDebateAnimation);
                }
                if (pauseBtn) {
                    pauseBtn.addEventListener('click', pauseCourtDebateAnimation);
                }
                if (skipBtn) {
                    skipBtn.addEventListener('click', skipCourtDebateAnimation);
                }
            }, 100);
        }

        // 模拟法庭辩论数据
        const courtDebateData = [
            {
                speaker: 'judge',
                name: '审判长',
                content: '现在开庭。今天审理李明、张丽诉王建国房屋买卖合同纠纷一案。根据《民事诉讼法》相关规定，本庭将依法公正审理此案。请原告代理人详细陈述案件事实经过、争议焦点以及具体的诉讼请求，并提供相关证据材料。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '尊敬的审判长，我方当事人李明、张丽夫妇于2024年11月18日与被告王建国签订《房屋买卖合同》，约定购买被告位于海淀区万柳地区的房屋，成交价680万元，支付定金20万元。合同明确约定2025年1月31日前完成过户，且被告保证房屋无任何权利限制。然而，我方后来发现该房屋早在2024年9月15日就被法院查封，被告明知此情况却故意隐瞒，构成合同欺诈。现请求法院确认合同解除，判令被告双倍返还定金40万元，并赔偿我方损失75万元。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '审判长，我方认为原告的指控缺乏事实和法律依据。首先，我方当事人对房屋查封事实并不知情，签约时确实不知道房屋已被查封。其次，我方一直在积极努力解决查封问题，并非恶意违约。房屋查封是因为第三方债务纠纷导致，属于不可抗力因素。原告主张的损失数额过高且缺乏合理依据，特别是所谓的房价上涨损失纯属投机行为，不应得到法律保护。我方请求法院驳回原告的全部诉讼请求。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '请原告方就被告明知房屋查封事实这一关键争议焦点提供具体证据。同时，请详细说明各项损失的计算依据和合理性。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '审判长，关于被告明知查封事实，我方提供以下证据：第一，不动产查询结果显示房屋于2024年9月15日被查封，而我方签约时间为11月18日，时间差达2个月；第二，查封系因被告自身债务纠纷导致，被告作为被执行人必然知情；第三，我方与被告的微信聊天记录显示，被告在协商过程中曾承认知道查封情况。关于损失，我方急售现房损失30万有买卖合同为证，各项费用支出5万有发票凭证，房价上涨损失40万有市场调研报告支撑。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '审判长，我方坚决否认明知查封事实。第一，执行案件的查封通知书送达存在程序问题，我方当事人当时不在住所地，未实际收到通知；第二，原告所谓的微信聊天记录断章取义，我方只是在事后得知查封情况后表示会积极解决；第三，原告的损失计算明显夸大，特别是房价上涨损失纯属市场投机，与合同履行无直接因果关系。我方认为即使存在违约，也应按实际损失赔偿，不应适用定金罚则。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '现在请双方就定金罚则的适用条件进行辩论。请原告方说明本案是否符合"致使不能实现合同目的"的法定条件。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '审判长，本案完全符合定金罚则的适用条件。根据《民法典》第587条规定，收受定金的一方不履行债务或者履行债务不符合约定，致使不能实现合同目的的，应当双倍返还定金。本案中，被告因房屋被查封无法办理过户，这是合同的核心义务，其不履行直接导致合同目的无法实现。而且被告的行为具有主观恶意，明知查封仍签约收取定金，更应承担加重责任。定金罚则的立法目的就是惩罚违约方、保护守约方，本案情形完全符合。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '我方认为不应适用定金罚则。第一，定金罚则适用需要严格的条件，即"致使不能实现合同目的"，而本案中我方一直在积极努力解决查封问题，并非完全不履行；第二，房屋查封是因第三方债务纠纷导致，属于客观情况变化，不完全归责于我方；第三，原告在知晓查封情况后仍有一段时间的观望期，说明合同目的并非完全不能实现；第四，定金罚则作为惩罚性条款，应当严格适用，不能随意扩大解释。我方建议按实际损失赔偿即可。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '请双方就房价上涨损失的认定问题进行辩论。原告方请说明该项损失的计算依据和合理性，被告方可就此发表反驳意见。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '关于房价上涨损失，我方有充分的事实和法律依据。第一，我方因信赖合同履行，错过了购买其他房源的机会，这是可预见的损失；第二，我方提供的市场调研报告显示，同地段同类房屋价格从680万上涨至720万以上，涨幅客观真实；第三，根据《民法典》第584条，因违约造成的损失包括合同履行后可以获得的利益，但不得超过违约方订立合同时预见到或者应当预见到的损失；第四，房地产市场价格波动是正常现象，被告作为房屋出售方应当预见到延迟履行可能导致的市场风险。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '我方坚决反对原告关于房价上涨损失的主张。第一，房价上涨属于市场风险，不应由违约方承担，这违背了风险分配的基本原则；第二，原告所谓的市场调研报告缺乏权威性，不能作为损失认定的依据；第三，原告主张的40万损失明显过高，超出了合理预期范围；第四，根据合同相对性原则，我方只对合同相对方承担责任，不应承担市场波动风险；第五，原告完全可以通过其他途径购买类似房屋，其选择等待本合同履行是自主决定，应自担风险。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '请双方就合同欺诈的认定标准进行最后辩论。原告方请说明被告行为如何构成欺诈，被告方请就此进行最后陈述。'
            },
            {
                speaker: 'plaintiff',
                name: '原告代理人',
                content: '被告的行为完全符合合同欺诈的构成要件。第一，客观上，被告故意隐瞒房屋查封的重大事实，这是影响合同订立的关键信息；第二，主观上，被告明知房屋被查封仍与我方签约收取定金，具有明显的欺诈故意；第三，因果关系上，我方正是基于对被告承诺的信赖才签订合同，如果事先知晓查封情况绝不会签约；第四，后果上，被告的欺诈行为直接导致我方遭受重大经济损失。根据《民法典》第148条，被告应承担相应法律责任。'
            },
            {
                speaker: 'defendant',
                name: '被告代理人',
                content: '我方最后陈述如下：第一，我方当事人对查封事实确实不知情，不存在故意隐瞒的主观恶意；第二，即使存在信息不对称，也不能简单认定为欺诈，需要综合考虑各种因素；第三，房屋买卖中，买方有义务进行必要的尽职调查，原告未尽到合理注意义务；第四，我方一直在积极努力解决问题，体现了诚信履约的态度；第五，请求法院综合考虑案件实际情况，公平合理地处理本案争议，不应过度加重我方责任。'
            },
            {
                speaker: 'judge',
                name: '审判长',
                content: '经过充分的法庭调查、举证质证和法庭辩论，本案的争议焦点已经非常明确。双方围绕合同欺诈认定、合同解除条件、定金罚则适用、损失赔偿范围等核心问题进行了深入辩论。本庭认为，本案涉及房屋买卖合同中的诚信履约问题，关系到房地产交易秩序的维护。现在法庭调查和辩论环节已经结束，本庭将根据查明的事实，依照《民法典》关于合同欺诈、合同解除、定金罚则、损害赔偿等相关规定，结合最高人民法院关于房屋买卖合同纠纷的司法解释精神，对本案进行全面审理。现在宣布休庭，本庭将依法进行评议，并在法定期限内择日公开宣判。'
            }
        ];

        let currentMessageIndex = 0;
        let isDebateRunning = false;
        let debateInterval = null;

        // 开始庭审辩论动画
        function startCourtDebateAnimation() {
            if (isDebateRunning) return;

            isDebateRunning = true;
            currentMessageIndex = 0;

            const startBtn = document.getElementById('startCourtDebate');
            const pauseBtn = document.getElementById('pauseCourtDebate');
            const skipBtn = document.getElementById('skipCourtDebate');
            const dialogueContainer = document.getElementById('courtDialogue');

            // 更新按钮状态
            startBtn.style.display = 'none';
            pauseBtn.style.display = 'inline-block';
            skipBtn.style.display = 'inline-block';

            // 清空对话容器
            dialogueContainer.innerHTML = '';

            // 开始显示对话
            showNextMessage();
        }

        // 显示下一条消息
        function showNextMessage() {
            if (currentMessageIndex >= courtDebateData.length) {
                // 辩论结束，显示总结
                finishCourtDebate();
                return;
            }

            const message = courtDebateData[currentMessageIndex];
            const dialogueContainer = document.getElementById('courtDialogue');

            // 创建消息元素
            const messageElement = document.createElement('div');
            messageElement.className = `dialogue-message ${message.speaker}`;

            messageElement.innerHTML = `
                <div class="message-avatar">${getAvatarEmoji(message.speaker)}</div>
                <div class="message-bubble">
                    <div class="message-speaker">${message.name}</div>
                    <div class="message-content" id="message-${currentMessageIndex}">
                        <div class="loading-dots">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                </div>
            `;

            dialogueContainer.appendChild(messageElement);

            // 滚动到底部
            dialogueContainer.scrollTop = dialogueContainer.scrollHeight;

            // 随机延迟1-2秒后开始打字机效果
            const randomDelay = Math.random() * 1000 + 1000; // 1000-2000ms
            setTimeout(() => {
                if (!isDebateRunning) return;

                // 移除加载动画，开始打字机效果
                const contentElement = document.getElementById(`message-${currentMessageIndex}`);
                contentElement.innerHTML = '';
                contentElement.classList.add('typing-cursor');

                typeWriterEffect(contentElement, message.content, 30, () => {
                    // 移除打字机光标
                    contentElement.classList.remove('typing-cursor');

                    // 延迟后显示下一条消息
                    setTimeout(() => {
                        if (isDebateRunning) {
                            currentMessageIndex++;
                            showNextMessage();
                        }
                    }, 1500);
                });
            }, randomDelay);
        }

        // 获取头像表情符号
        function getAvatarEmoji(speaker) {
            switch(speaker) {
                case 'judge': return '⚖️';
                case 'plaintiff': return '👨‍💼';
                case 'defendant': return '🏢';
                default: return '👤';
            }
        }

        // 打字机效果
        function typeWriterEffect(element, text, speed, callback) {
            let i = 0;
            element.textContent = '';

            function type() {
                if (i < text.length && isDebateRunning) {
                    element.textContent += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else if (i >= text.length) {
                    if (callback) callback();
                }
            }

            type();
        }

        // 暂停/继续庭审辩论
        function pauseCourtDebateAnimation() {
            const pauseBtn = document.getElementById('pauseCourtDebate');

            if (isDebateRunning) {
                isDebateRunning = false;
                pauseBtn.textContent = '继续';
            } else {
                isDebateRunning = true;
                pauseBtn.textContent = '暂停';
                showNextMessage();
            }
        }

        // 中止庭审辩论
        function skipCourtDebateAnimation() {
            isDebateRunning = false;

            const dialogueContainer = document.getElementById('courtDialogue');

            // 添加中止提示消息
            const stopMessage = document.createElement('div');
            stopMessage.className = 'dialogue-message judge';
            stopMessage.innerHTML = `
                <div class="message-avatar">⚖️</div>
                <div class="message-bubble">
                    <div class="message-speaker">审判长</div>
                    <div class="message-content" style="color: #e74c3c; font-weight: 500;">庭审辩论已中止。</div>
                </div>
            `;
            dialogueContainer.appendChild(stopMessage);

            // 滚动到底部
            dialogueContainer.scrollTop = dialogueContainer.scrollHeight;

            // 更新按钮状态
            const startBtn = document.getElementById('startCourtDebate');
            const pauseBtn = document.getElementById('pauseCourtDebate');
            const skipBtn = document.getElementById('skipCourtDebate');

            startBtn.style.display = 'inline-block';
            startBtn.textContent = '重新开始';
            pauseBtn.style.display = 'none';
            skipBtn.style.display = 'none';
        }

        // 完成庭审辩论
        function finishCourtDebate() {
            isDebateRunning = false;

            const startBtn = document.getElementById('startCourtDebate');
            const pauseBtn = document.getElementById('pauseCourtDebate');
            const skipBtn = document.getElementById('skipCourtDebate');
            const summaryElement = document.getElementById('courtSummary');
            const nextButton = document.getElementById('viewLegalDocumentBtn');

            // 更新按钮状态
            startBtn.textContent = '重新开始';
            startBtn.style.display = 'inline-block';
            pauseBtn.style.display = 'none';
            skipBtn.style.display = 'none';

            // 显示总结
            summaryElement.style.display = 'block';
            summaryElement.scrollIntoView({ behavior: 'smooth' });

            // 动画显示论证强度
            setTimeout(() => {
                const probabilityFill = document.querySelector('.probability-fill');
                if (probabilityFill) {
                    probabilityFill.style.width = '75%';
                }

                // 辩论完成后显示"查看完整分析报告与起诉文书"按钮
                if (nextButton) {
                    nextButton.style.display = 'block';
                    nextButton.style.animation = 'fadeIn 0.5s ease';
                }
            }, 500);
        }
        // 加载起诉文书内容
        function loadLegalDocumentContent() {
            const container = document.getElementById('legalDocumentContent');
            if (!container) return;

            container.innerHTML = `
                <div class="document-content">
                    <div class="document-header">
                        <h3>民事起诉状</h3>
                    </div>

                    <div style="margin: 2rem 0;">
                        <div style="background: rgba(245, 245, 245, 0.5); border-radius: 8px; padding: 1rem; margin: 1rem 0; line-height: 1.8;">
                            <p><strong>原告：</strong>李明，男，1985年3月15日生，汉族</p>
                            <p>身份证号：11010819850315XXXX</p>
                            <p>住址：北京市海淀区中关村大街XX号院X号楼X单元XXX室</p>
                            <p>联系电话：138-0010-8888</p>
                        </div>
                        <div style="background: rgba(245, 245, 245, 0.5); border-radius: 8px; padding: 1rem; margin: 1rem 0; line-height: 1.8;">
                            <p><strong>原告：</strong>张丽，女，1987年7月22日生，汉族</p>
                            <p>身份证号：11010819870722XXXX</p>
                            <p>住址：北京市海淀区中关村大街XX号院X号楼X单元XXX室</p>
                            <p>联系电话：139-0010-9999</p>
                        </div>
                        <div style="background: rgba(245, 245, 245, 0.5); border-radius: 8px; padding: 1rem; margin: 1rem 0; line-height: 1.8;">
                            <p><strong>被告：</strong>王建国，男，1975年12月8日生，汉族</p>
                            <p>身份证号：11010819751208XXXX</p>
                            <p>住址：北京市海淀区万柳东路XX号院X号楼X单元XXX室</p>
                            <p>联系电话：136-0010-7777</p>
                        </div>
                        <div style="text-align: center; font-size: 1.1rem; font-weight: 500; margin: 2rem 0; padding: 1rem; background: rgba(179, 205, 224, 0.1); border-radius: 8px;">
                            <p><strong>案由：</strong>房屋买卖合同纠纷</p>
                        </div>
                    </div>

                    <div style="margin: 2rem 0;">
                        <h4 style="color: #333; font-size: 1.2rem; font-weight: 500; margin-bottom: 1rem;">诉讼请求：</h4>
                        <ol style="padding-left: 2rem;">
                            <li style="margin: 0.5rem 0; line-height: 1.8;">一、确认原告与被告于2024年11月18日签订的《房屋买卖合同》解除；</li>
                            <li style="margin: 0.5rem 0; line-height: 1.8;">二、判令被告立即退还原告定金人民币200,000元；</li>
                            <li style="margin: 0.5rem 0; line-height: 1.8;">三、判令被告向原告支付定金罚则人民币200,000元；</li>
                            <li style="margin: 0.5rem 0; line-height: 1.8;">四、判令被告赔偿原告因合同解除造成的损失人民币750,000元；</li>
                            <li style="margin: 0.5rem 0; line-height: 1.8;">五、判令被告承担本案全部诉讼费用。</li>
                        </ol>
                    </div>

                    <div style="margin: 2rem 0;">
                        <h4 style="color: #333; font-size: 1.2rem; font-weight: 500; margin-bottom: 1rem;">事实与理由：</h4>
                        <div style="line-height: 1.8; text-indent: 2em;">
                            <p><strong>一、合同签订及履行情况</strong></p>
                            <p>2024年11月18日，原告李明、张丽（系夫妻关系）与被告王建国签订《房屋买卖合同》，约定购买被告位于北京市海淀区万柳东路XX号院X号楼X单元XXX室的房屋，建筑面积126平方米，成交价格为人民币6,800,000元。合同约定原告支付定金200,000元，剩余房款6,600,000元在办理过户手续时一次性支付。合同明确约定过户时间为2025年1月31日前，被告保证该房屋权属清晰，无抵押、查封等权利限制。</p>

                            <p><strong>二、被告隐瞒房屋查封事实，构成欺诈</strong></p>
                            <p>经原告于2024年12月底向北京市不动产登记中心查询发现，涉案房屋早在2024年9月15日即被北京市海淀区人民法院依据（2024）京0108执保1234号执行裁定书予以查封，查封期限至2027年9月14日。该查封系因被告王建国作为被执行人，在李某某与王建国民间借贷纠纷案中未履行生效判决确定的给付义务（本金3,200,000元及相应利息），债权人李某某申请强制执行所致。被告在签订合同时对此查封事实完全知情，却故意向原告隐瞒。</p>

                            <p><strong>三、被告违约在先且拒绝承担责任</strong></p>
                            <p>合同约定的过户期限为2025年1月31日，但直至该期限届满，被告仍无法提供符合过户条件的房屋。原告多次催促，被告仅以'正在想办法解除查封'等理由搪塞。面对原告合理的解约要求，被告不仅拒绝退还定金，反而声称系原告违约在先。</p>

                            <p><strong>四、法律依据</strong></p>
                            <p>根据《中华人民共和国民法典》第一百四十八条、第五百六十三条、第五百八十七条的规定，一方以欺诈手段使对方在违背真实意思的情况下实施的民事法律行为，受欺诈方有权请求人民法院予以撤销；当事人一方迟延履行主要债务，经催告后在合理期限内仍未履行的，对方可以解除合同；收受定金的一方不履行债务或者履行债务不符合约定，致使不能实现合同目的的，应当双倍返还定金。</p>
                        </div>
                    </div>

                    <p style="margin: 2rem 0; line-height: 1.8;"><strong>综上所述，</strong>原告的诉讼请求事实清楚、理由充分、法律依据明确，请求人民法院依法支持原告的全部诉讼请求。</p>

                    <div style="text-align: right; margin-top: 3rem;">
                        <p>此致</p>
                        <p><strong>北京市海淀区人民法院</strong></p>
                        <br>
                        <p>起诉人：李明（手写签名）</p>
                        <p>起诉人：张丽（手写签名）</p>
                        <p>二〇二五年二月十五日</p>
                    </div>

                    <div style="margin-top: 2rem; padding: 1rem; background: rgba(245, 166, 35, 0.1); border-radius: 8px;">
                        <h5 style="color: #F5A623; margin-bottom: 0.5rem;">附件清单：</h5>
                        <p>1. 房屋买卖合同原件及定金收据</p>
                        <p>2. 北京市不动产登记中心房屋查询结果</p>
                        <p>3. 法院执行裁定书</p>
                        <p>4. 与被告沟通记录</p>
                        <p>5. 损失证明材料</p>
                        <p>6. 其他相关证据材料</p>
                    </div>
                </div>
            `;
        }



        // 下载完整报告功能
        function downloadReport() {
            log('下载完整报告');

            // 创建报告内容
            const reportContent = `法弈智能法律诉讼辅助系统 - 完整分析报告

案件概况：
- 案件类型：房屋买卖合同纠纷
- 争议金额：1,550,000元（定金20万+赔偿135万）
- 预估胜率：77%
- 证据完整度：78%

证据分析：
已有证据：房屋买卖合同、定金收据、不动产查询结果、法院执行裁定书、微信聊天记录、急售房屋合同、费用支出凭证等7项
关键缺失：被告明知查封的直接证据、房价上涨的权威评估报告2项

法律依据：
- 《中华人民共和国民法典》第一百四十八条（欺诈撤销）
- 《中华人民共和国民法典》第五百六十三条（合同解除）
- 《中华人民共和国民法典》第五百八十七条（定金罚则）

策略建议：
1. 申请法院调取被告在执行案件中的相关材料
2. 委托专业房地产评估机构出具市场价格分析报告
3. 整理完善与被告的所有沟通记录
4. 准备应对被告可能提出的反驳

生成时间：${new Date().toLocaleString()}`;

            downloadTextFile(reportContent, '法弈分析报告.txt');
        }

        // 下载起诉文书功能
        function downloadDocument() {
            log('下载起诉文书');

            // 获取起诉状内容
            const documentContent = `民事起诉状

原告：李明，男，1985年3月15日出生，汉族，身份证号：11010819850315XXXX，住址：北京市海淀区中关村大街XX号院X号楼X单元XXX室，联系电话：138-0010-8888。

原告：张丽，女，1987年7月22日出生，汉族，身份证号：11010819870722XXXX，住址：北京市海淀区中关村大街XX号院X号楼X单元XXX室，联系电话：139-0010-9999。

被告：王建国，男，1975年12月8日出生，汉族，身份证号：11010819751208XXXX，住址：北京市海淀区万柳东路XX号院X号楼X单元XXX室，联系电话：136-0010-7777。

案由：房屋买卖合同纠纷

诉讼请求：
一、确认原告与被告于2024年11月18日签订的《房屋买卖合同》解除；
二、判令被告立即退还原告定金人民币200,000元；
三、判令被告向原告支付定金罚则人民币200,000元；
四、判令被告赔偿原告因合同解除造成的损失人民币750,000元；
五、判令被告承担本案全部诉讼费用。

事实与理由：
（详细内容请参考完整文书）

此致
北京市海淀区人民法院

起诉人：李明（手写签名）
起诉人：张丽（手写签名）
二〇二五年二月十五日

附项：
1. 本起诉状副本1份；
2. 证据材料复印件1套；
3. 原告身份证复印件各1份；
4. 房屋买卖合同复印件1份。`;

            downloadTextFile(documentContent, '民事起诉状.txt');
        }

        // 通用文本文件下载函数
        function downloadTextFile(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成，开始初始化');

            // 绑定事件
            const startBtn = document.getElementById('startExperienceBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const viewReportBtn = document.getElementById('viewReportBtn');
            const viewLegalInsightsBtn = document.getElementById('viewLegalInsightsBtn');
            const viewCourtSimulationBtn = document.getElementById('viewCourtSimulationBtn');
            const viewLegalDocumentBtn = document.getElementById('viewLegalDocumentBtn');
            const backToEvidenceBtn = document.getElementById('backToEvidenceBtn');
            const backToLegalInsightsBtn = document.getElementById('backToLegalInsightsBtn');
            const backToCourtSimulationBtn = document.getElementById('backToCourtSimulationBtn');
            const downloadReportBtn = document.getElementById('downloadReportBtn');
            const downloadDocumentBtn = document.getElementById('downloadDocumentBtn');

            if (startBtn) {
                startBtn.addEventListener('click', switchToExperiencePage);
                log('立即体验按钮事件绑定成功');
            } else {
                log('错误：找不到立即体验按钮');
            }

            if (analyzeBtn) {
                analyzeBtn.addEventListener('click', startAnalysis);
                log('立即分析按钮事件绑定成功');
            }

            if (viewReportBtn) {
                viewReportBtn.addEventListener('click', showEvidenceChain);
                log('查看报告按钮事件绑定成功');
            }

            if (viewLegalInsightsBtn) {
                viewLegalInsightsBtn.addEventListener('click', showLegalInsights);
                log('查看法律分析按钮事件绑定成功');
            }

            if (viewCourtSimulationBtn) {
                viewCourtSimulationBtn.addEventListener('click', showCourtSimulation);
                log('查看模拟法庭按钮事件绑定成功');
            }

            if (viewLegalDocumentBtn) {
                viewLegalDocumentBtn.addEventListener('click', showLegalDocument);
                log('查看起诉文书按钮事件绑定成功');
            }

            if (backToEvidenceBtn) {
                backToEvidenceBtn.addEventListener('click', backToEvidenceChain);
                log('返回证据链分析按钮事件绑定成功');
            }

            if (backToLegalInsightsBtn) {
                backToLegalInsightsBtn.addEventListener('click', backToLegalInsights);
                log('返回法律分析按钮事件绑定成功');
            }

            if (backToCourtSimulationBtn) {
                backToCourtSimulationBtn.addEventListener('click', backToCourtSimulation);
                log('返回模拟法庭按钮事件绑定成功');
            }

            if (downloadReportBtn) {
                downloadReportBtn.addEventListener('click', downloadReport);
                log('下载报告按钮事件绑定成功');
            }

            if (downloadDocumentBtn) {
                downloadDocumentBtn.addEventListener('click', downloadDocument);
                log('下载文书按钮事件绑定成功');
            }

            log('初始化完成');
        });

        log('脚本加载完成');
    </script>
</body>
</html>
